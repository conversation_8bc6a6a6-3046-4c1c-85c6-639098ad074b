package dto.user;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.*;
import java.util.*;
import org.apache.commons.lang3.StringUtils;

public class ColivingUserConfirmationParameters extends ConfirmationTokenParameters {

    protected String password;

    public ColivingUserConfirmationParameters(JsonNode body) throws APIException {
        super(body);
        this.password = safeString("password", body, "");
    }

    public ColivingUserConfirmationParameters validate() throws APIException {
        super.validate();
        if (StringUtils.isBlank(this.password)) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        return this;
    }

    public String getPassword() {
        return this.password;
    }
}
