package dto.building;

import com.fasterxml.jackson.databind.JsonNode;
import dto.maintenance.MaintenanceParameterParameters;
import global.APIException;
import global.APIException.*;
import models.Building;

public class AssignMaintenanceParameterToBuildingParameters extends MaintenanceParameterParameters {

    protected Building building;

    public AssignMaintenanceParameterToBuildingParameters(JsonNode body, int buildingId)
        throws APIException {
        super(body);
        building = Building.findById(buildingId);
    }

    public AssignMaintenanceParameterToBuildingParameters validate() throws APIException {
        super.validate();

        if (building == null) throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);

        return this;
    }

    public Building getBuilding() {
        return this.building;
    }
}
