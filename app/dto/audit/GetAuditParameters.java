package dto.audit;

import dto.BaseFilterParameters;
import global.APIException;
import java.util.Map;

public class GetAuditParameters extends BaseFilterParameters {

    protected static final String ORDER_PARAM_DEFAULT = "creationTimestamp";

    public GetAuditParameters(Map<String, String[]> queryString) throws APIException {
        super(queryString);
        this.order = safeString(ORDER_PARAM, queryString, ORDER_PARAM_DEFAULT);
    }

    public GetAuditParameters validate() throws APIException {
        super.validate();

        if (this.order.equals("sender")) {
            this.order = "UUID_Sender";
        } else if (this.order.equals("receiver")) {
            this.order = "UUID_Receiver";
        }

        return this;
    }
}
