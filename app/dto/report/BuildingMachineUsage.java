package dto.report;

import java.math.BigInteger;
import java.util.Date;

public class BuildingMachineUsage {

    private Integer buildingId;
    private String buildingName;
    private Integer machineId;
    private BigInteger uses;
    private Date lastMaintenance;
    private String maintenanceType;

    public BuildingMachineUsage() {}

    public BuildingMachineUsage(
        Object buildingId,
        Object buildingName,
        Object machineId,
        Object uses,
        Object lastMaintenance,
        Object maintenanceTypes
    ) {
        super();
        this.buildingId = (Integer) buildingId;
        this.buildingName = (String) buildingName;
        this.machineId = (Integer) machineId;
        this.uses = (BigInteger) uses;
        this.lastMaintenance = (Date) lastMaintenance;
        this.maintenanceType = (String) maintenanceTypes;
    }

    public Integer getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(Integer buildingId) {
        this.buildingId = buildingId;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public Integer getMachineId() {
        return machineId;
    }

    public void setMachineId(Integer machineId) {
        this.machineId = machineId;
    }

    public BigInteger getUses() {
        return uses;
    }

    public void setUses(BigInteger uses) {
        this.uses = uses;
    }

    public Date getLastMaintenance() {
        return lastMaintenance;
    }

    public void setLastMaintenance(Date lastMaintenance) {
        this.lastMaintenance = lastMaintenance;
    }

    public String getMaintenanceType() {
        return maintenanceType;
    }

    public void setMaintenanceType(String maintenanceType) {
        this.maintenanceType = maintenanceType;
    }
}
