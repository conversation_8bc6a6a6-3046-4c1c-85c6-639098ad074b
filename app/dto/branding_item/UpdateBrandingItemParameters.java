package dto.branding_item;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.BrandingItem;
import models.Rate;

public class UpdateBrandingItemParameters extends BrandingItemParameters {

    public UpdateBrandingItemParameters(BrandingItem brandingItem, JsonNode body)
        throws APIException {
        super(brandingItem, body);
    }

    @Override
    public UpdateBrandingItemParameters validate() throws APIException {
        super.validate();

        if (rateId != 0) {
            rate = Rate.findById(rateId);

            if (rate == null) {
                throw APIException.raise(APIException.APIErrors.RATE_NOT_FOUND);
            }
        }

        return this;
    }
}
