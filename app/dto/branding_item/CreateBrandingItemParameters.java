package dto.branding_item;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Rate;
import org.apache.commons.lang3.StringUtils;
import play.i18n.Messages;

public class CreateBrandingItemParameters extends BrandingItemParameters {

    public CreateBrandingItemParameters(JsonNode body) throws APIException {
        super(body);
    }

    @Override
    public CreateBrandingItemParameters validate() throws APIException {
        if (
            StringUtils.isBlank(description) ||
            StringUtils.isBlank(imageName) ||
            StringUtils.isBlank(name) ||
            StringUtils.isBlank(reference) ||
            rateId == 0
        ) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(
                    getMissingParametersMessage(
                        "description",
                        "imageName",
                        "name",
                        "rateId",
                        "reference"
                    )
                );
        }

        rate = Rate.findById(rateId);
        if (rate == null) {
            throw APIException.raise(APIException.APIErrors.RATE_NOT_FOUND);
        }

        return this;
    }

    public String getDescription() {
        return description;
    }

    public String getImageName() {
        return imageName;
    }

    public String getName() {
        return name;
    }

    public String getReference() {
        return reference;
    }

    public Rate getRate() {
        return rate;
    }
}
