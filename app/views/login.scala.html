@(message: String)

@main("LAVOMAT | Login") {
    <!-- headers tag -->
} {
    <!-- content tag -->

<body class="page-login" init-ripples ng-controller="LoginController">
    <div class="center">
        <div class="card bordered z-depth-2" style="margin:0% auto; max-width:400px;">
            <div class="card-header">
                <div class="brand-logo">
                    <div style="display:inline">
                        <img src="/assets/img/Lavomat_Logo.jpg" width=100>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <div class="m-b-30">
                    <div class="card-title strong pink-text">Login</div>
                    <p class="card-title-desc">
                        Portal de Gestión
                    </p>
                </div>
                <form class="form-floating">
                    <div class="form-group">
                        <label for="inputEmail" class="control-label">Email</label>
                        <input type="text" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="inputPassword" class="control-label">Password</label>
                        <input type="password" class="form-control" id="inputPassword">
                    </div>
                </form>
            </div>
            <div class="card-action clearfix">
                <div class="pull-right">
                    <button type="button" class="btn btn-link black-text">Olvide mi Contraseña</button>
                    <button type="button" class="btn btn-link black-text" ng-click="login()">Login</button>
                </div>
            </div>
        </div>
    </div>

    <script charset="utf-8" src="//code.jquery.com/jquery-1.11.3.min.js"></script>
    <script charset="utf-8" src="/pages/login.js"></script>

</body>

}
