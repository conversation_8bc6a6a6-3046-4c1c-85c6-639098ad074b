package policies.buildings;

import models.Building;
import models.Role;
import play.mvc.Http;
import policies.ApiClient;
import policies.BaseModelPolicy;
import policies.Level;
import queries.buildings.BuildingQuery;

public class BuildingPolicy extends BaseModelPolicy<Building> {

    protected final BuildingQuery query;

    public BuildingPolicy(Http.Context ctx) {
        super(ctx);
        this.query = new BuildingQuery();
    }

    protected BuildingQuery query() {
        return this.query;
    }

    @Override
    public Role[] creationPermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] readPermission() {
        return EVERYONE;
    }

    @Override
    public Role[] updatePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] deletePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        if (Role.BOT == role) {
            return Level.MEDIUM_LEVEL;
        } else if (client.match(ApiClient.ASSISTANT_WEB)) {
            return Level.MINIMAL_MEDIUM_LEVEL;
        } else if (client.match(ApiClient.BACKOFFICE)) {
            return Level.MEDIUM_HIGH_LEVEL;
        }

        return Level.MINIMUM_LEVEL;
    }
}
