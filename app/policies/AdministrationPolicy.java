package policies;

import models.Administration;
import models.Role;
import play.mvc.Http;
import queries.administrations.AdministrationQuery;

public class AdministrationPolicy extends BaseModelPolicy<Administration> {

    protected final AdministrationQuery query;

    public AdministrationPolicy(Http.Context ctx) {
        super(ctx);
        this.query = new AdministrationQuery();
    }

    protected AdministrationQuery query() {
        return this.query;
    }

    @Override
    public Role[] creationPermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] readPermission() {
        return new Role[] {
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.ASSISTANT,
            Role.SUPERVISOR,
        };
    }

    @Override
    public Role[] updatePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public Role[] deletePermission() {
        return new Role[] { Role.MASTER };
    }

    @Override
    public int level(ApiClient client) {
        super.level(client);

        if (ApiClient.MOBILE_APP == client) {
            // TODO: validate
            return Level.MINIMAL_MEDIUM_LEVEL;
        } else if (client != null) {
            if (
                client.match(ApiClient.BACKOFFICE) && this.role.match(Role.MASTER, Role.ASSISTANT)
            ) {
                return Level.MEDIUM_LEVEL;
            }
        }

        return Level.MINIMUM_LEVEL;
    }
}
