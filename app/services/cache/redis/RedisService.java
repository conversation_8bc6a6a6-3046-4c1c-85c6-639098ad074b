package services.cache.redis;

import static utils.ApplicationConfiguration.*;

import global.APIException;
import global.ExtendedSupplier;
import java.io.*;
import javax.inject.Singleton;
import redis.clients.jedis.*;
import services.cache.CacheService;
import utils.ApplicationConfiguration;

@Singleton
public class RedisService extends CacheService {

    private final JedisPool jedisPool;

    public RedisService() {
        super(isCacheEnabled());
        if (this.cacheEnabled) {
            HostAndPort hostAndPort = new HostAndPort(
                ApplicationConfiguration.getRedisHost(),
                Integer.parseInt(ApplicationConfiguration.getRedisPort())
            );

            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(32);
            poolConfig.setMaxIdle(6);
            poolConfig.setMinIdle(1);

            this.jedisPool =
                new JedisPool(
                    poolConfig,
                    hostAndPort,
                    DefaultJedisClientConfig
                        .builder()
                        .ssl(isProd() || isSandbox())
                        .socketTimeoutMillis(5000) // set timeout to 5 seconds
                        .connectionTimeoutMillis(5000) // set connection timeout to 5 seconds
                        .build()
                );

            play.Logger.info("Redis Service initialized and enabled.");
        } else {
            this.jedisPool = null;
            play.Logger.info("Redis Service is disabled in this environment.");
        }
    }

    public <T extends Serializable> T cache(String key, ExtendedSupplier<T> getter)
        throws APIException {
        boolean getterRan = false;
        String cacheKey = getCacheKey(key);
        T value = null;
        if (!this.cacheEnabled) {
            play.Logger.debug("Redis is disabled. Bypassing cache for key: {}", cacheKey);
            return getter.get();
        }

        try (Jedis jedis = this.jedisPool.getResource()) {
            value = deserialize(jedis.get(cacheKey.getBytes()));
            if (value != null) {
                play.Logger.debug("Cache hit for key: {}", cacheKey);
                return value;
            }

            value = getter.get();
            getterRan = true;
            if (setCacheValue(jedis, cacheKey, value)) {
                play.Logger.debug("Cache miss for key: {}. Value set in cache.", cacheKey);
            }
            return value;
        } catch (APIException e) {
            play.Logger.error("API exception when caching key: {}", cacheKey, e);
            throw e;
        } catch (Exception e) {
            play.Logger.error(
                "Error in cache operation for key {}: {}",
                cacheKey,
                e.getMessage(),
                e
            );
            if (getterRan) {
                return value;
            }
            return getter.get(); // Fallback to compute value if there's an error with Redis
        }
    }

    @Override
    public void invalidate(String key) {
        play.Logger.info("Invalidating cache for key: {}", key);
        try (Jedis jedis = this.jedisPool.getResource()) {
            jedis.del(getCacheKey(key).getBytes());
        } catch (Exception e) {
            play.Logger.error("Error invalidating cache for key {}: {}", key, e.getMessage(), e);
        }
    }

    private boolean setCacheValue(Jedis jedis, String key, Serializable value) {
        try {
            jedis.set(key.getBytes(), serialize(value));
            return true;
        } catch (Exception e) {
            play.Logger.error(
                "Error setting value in cache for key {}: {}",
                key,
                e.getMessage(),
                e
            );
            return false;
        }
    }

    private <T extends Serializable> byte[] serialize(T obj) {
        try (
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos)
        ) {
            oos.writeObject(obj);
            return bos.toByteArray();
        } catch (IOException e) {
            play.Logger.error(
                "Serialization error for object of class {}: {}",
                obj.getClass().getName(),
                e.getMessage(),
                e
            );
            return new byte[0];
        }
    }

    @SuppressWarnings("unchecked")
    private <T extends Serializable> T deserialize(byte[] data) {
        if (data == null) return null;
        try (
            ByteArrayInputStream bis = new ByteArrayInputStream(data);
            ObjectInputStream ois = new ObjectInputStream(bis)
        ) {
            return (T) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            play.Logger.error("Deserialization error: {}", e.getMessage(), e);
            return null;
        }
    }

    public void close() {
        if (this.jedisPool != null) {
            this.jedisPool.destroy();
        }
    }
}
