package services.google;

import global.APIException;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import javax.net.ssl.HttpsURLConnection;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import policies.ApiClient;
import services.BaseService;
import utils.ApplicationConfiguration;

public class GoogleReCaptchaService extends BaseService {

    private final String SCORE_KEY = "score";
    private final String SUCCESS_KEY = "success";

    private final double THRESHOLD_DEFAULT = 0.5;
    private final double THRESHOLD_ASSISTANT = 0.3;

    /**
     * Validates if the token provided is safe
     * @param recaptchaToken token get on client
     * @param input provided data associated to the reCaptcha request
     */
    public boolean validate(String recaptchaToken, String input) throws APIException {
        return validate(recaptchaToken, input, null);
    }

    /**
     * Validates if the token provided is safe
     * @param recaptchaToken token get on client
     * @param input provided data associated to the reCaptcha request
     * @param client indicates which client initiates the validation
     */
    public boolean validate(String recaptchaToken, String input, ApiClient client)
        throws APIException {
        if (StringUtils.isBlank(recaptchaToken)) {
            throw APIException.raise(APIException.APIErrors.MISSING_PARAMETERS);
        }

        boolean success = false;
        double score = 0.0;
        double threshold = this.getThresholdByClient(client);

        try {
            URL urlObject = new URL(ApplicationConfiguration.getGoogleRecaptchaUrl());
            HttpsURLConnection connection = (HttpsURLConnection) urlObject.openConnection();

            connection.setRequestMethod("POST");
            connection.setRequestProperty("Accept-Language", "en-US,en;q=0.5");

            // add client result as post parameter
            String postParams =
                "secret=" +
                ApplicationConfiguration.getGoogleRecaptchaSecret() +
                "&response=" +
                recaptchaToken;

            // send post request to google recaptcha server
            connection.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
            wr.writeBytes(postParams);
            wr.flush();
            wr.close();

            BufferedReader in = new BufferedReader(
                new InputStreamReader(connection.getInputStream())
            );
            String inputLine;
            StringBuffer response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            JSONObject json = new JSONObject(response.toString());
            logger("Recaptcha response: {}", json.toString());

            success = json.getBoolean(this.SUCCESS_KEY);
            score = json.getDouble(this.SCORE_KEY);

            if (!success || score < threshold) {
                logger(
                    "Recaptcha failed for {} - score: {} - success: {} - threshold: {}",
                    input,
                    score,
                    success,
                    threshold
                );
            }
        } catch (IOException | JSONException ex) {
            loggerError(
                "Unable to communicate with Google reCaptcha or parse the closureData: {}",
                ex.getMessage()
            );
            ex.printStackTrace();
        }

        if (ApplicationConfiguration.isLocal()) {
            return true;
        }

        // result should be sucessfull and spam score above the threshold
        return success && score >= threshold;
    }

    private double getThresholdByClient(ApiClient client) {
        if (client == null) return this.THRESHOLD_DEFAULT;

        switch (client) {
            case ASSISTANT_WEB:
                return this.THRESHOLD_ASSISTANT;
            default:
                return this.THRESHOLD_DEFAULT;
        }
    }
}
