package services.building;

import global.APIException;
import models.Building;

public class BuildingRpiService {

    private Building building;

    private BuildingRpiService() {}

    public BuildingRpiService(int buildingId) throws APIException {
        this.building = Building.findById(buildingId);
        if (this.building == null) {
            throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
        }
    }

    public int getRpiCount() {
        return (
            (int) building
                .getMachines()
                .stream()
                .filter(machine -> machine.getSortIndex() > 0)
                .count() -
            (int) building
                .getMachines()
                .stream()
                .filter(machine -> machine.getRPIChild() != null && machine.getSortIndex() > 0)
                .count()
        );
    }
}
