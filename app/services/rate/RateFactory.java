package services.rate;

import domains.back_office.dto.rates.UpdateParameters;
import global.APIException;
import global.APIException.APIErrors;
import models.Rate;

public class RateFactory {

    protected Rate rate;

    public RateFactory(Rate rate) {
        this.rate = rate;
    }

    public void create() {
        this.rate.save();
    }

    public void update(UpdateParameters dto) throws APIException {
        if (this.rate == null) {
            throw APIException.raise(APIErrors.RATE_NOT_FOUND);
        }

        this.rate.setName(dto.getName());
        this.rate.setRateType(dto.getType());
        this.rate.setAppliesIVA(dto.getAppliesIVA());
        this.rate.setDescriptiveMessage(dto.getMessage());

        this.rate.update();
    }
}
