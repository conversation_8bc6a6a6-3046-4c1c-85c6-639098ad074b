package services.assistant;

import models.Building;
import utils.ApplicationConfiguration;

public class UrlService {

    public static String getUrlByBuilding(Building building) {
        String slug = building.getSlug();
        if (slug == null || slug.isEmpty()) {
            return "";
        }

        String url = ApplicationConfiguration.getAssistantUrl();
        return url + (url.endsWith("/") ? "" : "/") + slug;
    }
}
