package services.machine;

import models.Machine;
import models.MachineUse;
import models.MachineUseResult;
import org.joda.time.DateTime;
import queries.machine_uses.MachineUseCustomQuery;
import services.BaseService;
import utils.DateHelper;

import java.util.ArrayList;
import java.util.List;

import static models.MachineUse.ACCREDITED_RESULT;

public class MachineUsabilityService extends BaseService {

    private final Availability availability;
    private MachineUse lastUse;

    public MachineUsabilityService(Machine machine) {
        this.availability = new Availability();
        process(machine);
    }

    public String getStatus() {
        return this.availability.status.toString();
    }

    public boolean isAvailable() {
        return Availability.Status.AVAILABLE == this.availability.status;
    }

    public boolean isInMaintenance() {
        return Availability.Status.IN_MAINTENANCE == this.availability.status;
    }


    public long getRemainingTime() {
        return this.availability.remainingTime;
    }

    public MachineUse getLastUse() {
        return this.lastUse;
    }

    private void process(Machine machine) {
        List<MachineUseResult> resultList = new ArrayList<>(ACCREDITED_RESULT);
        resultList.add(MachineUseResult.IN_MAINTENANCE);
        resultList.add(MachineUseResult.MAINTENANCE_INTERRUPTED);

        this.lastUse = new MachineUseCustomQuery()
            .getLastMachineUseByMachine(machine, resultList.toArray(new MachineUseResult[0]));

        if (this.lastUse == null) return;

        DateTime current = DateHelper.getUruguayanCurrentDateTime();
        DateTime machineCycleStart = new DateTime(this.lastUse.getTimestamp());
        DateTime machineUseCycleEnd = machineCycleStart.plusMinutes(machine.getAverageUseTime());
        DateTime maintenanceCycleEnd = machineCycleStart.plusMinutes(100);

        MachineUseResult lastResult = MachineUseResult.getEnum(this.lastUse.getResult());
        boolean machineUseCycleFinished = machineUseCycleEnd.isBefore(current);
        boolean maintenanceCycleFinished = maintenanceCycleEnd.isBefore(current);

        boolean inMaintenance = !maintenanceCycleFinished && lastResult == MachineUseResult.IN_MAINTENANCE;

        if (inMaintenance) {
            this.availability.status = Availability.Status.IN_MAINTENANCE;
        } else if (machineUseCycleFinished || lastResult == MachineUseResult.MAINTENANCE_INTERRUPTED) {
            this.availability.status = Availability.Status.AVAILABLE;
        } else {
            this.availability.status = Availability.Status.IN_USE;
        }

        if (inMaintenance) {
            this.availability.remainingTime = maintenanceCycleEnd.getMillis() - current.getMillis();
        } else if (!machineUseCycleFinished) {
            this.availability.remainingTime = machineUseCycleEnd.getMillis() - current.getMillis();
        }
    }

    private static class Availability {

        private enum Status {
            AVAILABLE,
            IN_USE,
            IN_MAINTENANCE,
        }

        public Status status = Status.AVAILABLE;
        public long remainingTime = 0;
    }
}
