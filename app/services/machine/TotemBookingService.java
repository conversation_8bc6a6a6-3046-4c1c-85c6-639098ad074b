package services.machine;

import domains.activations.mqtt.LMMQTTProducer;
import domains.activations.mqtt.MessageBuilder;
import global.APIException;
import global.exceptions.machines.MachineAlreadyBooked;
import global.exceptions.machines.MachineBookingConfirmationOutOfTimeException;
import java.util.Date;
import models.Machine;
import models.MachineBooking;
import models.MachineUseResult;
import models.User;
import org.joda.time.DateTime;
import queries.booking.MachineBookingQuery;
import services.BaseService;
import utils.ApplicationConfiguration;
import utils.TimeZoneUtils;

public class TotemBookingService extends BaseService {

    public static final boolean ENABLED = ApplicationConfiguration.isTotemBookingEnabled();

    private static final long BLOCKING_TIME_WAITING_FOR_CONFIRMATION =
        TimeZoneUtils.A_SECOND_IN_MILLISECONDS * 15;

    private Machine machine;
    private MachineBooking booking;

    public TotemBookingService(Machine machine) {
        this.machine = machine;
    }

    public TotemBookingService(MachineBooking booking) {
        this.booking = booking;
    }

    public MachineBooking book(User user) throws MachineAlreadyBooked {
        if (!ENABLED) {
            return null;
        }

        assert this.machine != null;

        existsAnotherActiveBooking();
        create(user);
        publishCreation();

        return this.booking;
    }

    public void cancel(String reason) {
        if (!ENABLED) {
            return;
        }

        assert this.booking != null;
        logger("Cancellation requested - reason: {}", reason);

        if (
            MachineBooking.MachineBookingStatus.ACTIVATION_PENDENT != this.booking.getStatus() &&
            MachineBooking.MachineBookingStatus.IN_PROGRESS != this.booking.getStatus()
        ) {
            return;
        }

        this.booking.setStatus(MachineBooking.MachineBookingStatus.CANCELLED);
        this.booking.update();

        this.machine = this.booking.getMachine();
        publishCancellation();
    }

    public void confirm() throws APIException {
        if (!ENABLED) {
            return;
        }

        assert this.booking != null;
        assert MachineBooking.MachineBookingStatus.IN_PROGRESS == this.booking.getStatus();

        // startDate + 15'' >= now
        DateTime deadline = new DateTime(this.booking.getStartDate().getTime())
            .plus(BLOCKING_TIME_WAITING_FOR_CONFIRMATION);
        boolean isInTime = deadline.isEqualNow() || deadline.isAfterNow();

        if (isInTime) {
            this.booking.setStatus(MachineBooking.MachineBookingStatus.ACTIVATION_PENDENT);
            this.booking.update();
        } else {
            this.booking.setStatus(MachineBooking.MachineBookingStatus.EXPIRED);
            this.booking.update();

            throw new MachineBookingConfirmationOutOfTimeException();
        }
    }

    public MachineBookingQuery getActiveBookings() {
        if (!ENABLED) {
            return null;
        }

        return new MachineBookingQuery()
            .filterByMachineId(this.machine.getId())
            .filterByStartDate(
                // filtering by the current time less 15'' which is the oldest
                // time a booking could be created and still being confirmable
                DateTime.now().minus(BLOCKING_TIME_WAITING_FOR_CONFIRMATION).toDate()
            )
            .excludeByStatus(
                MachineBooking.MachineBookingStatus.CANCELLED,
                MachineBooking.MachineBookingStatus.EXPIRED
            );
    }

    /**
     * Mocked booking to be returned when the feature is disabled
     */
    public static MachineBooking getMockedBooking() {
        if (ENABLED) {
            return null;
        }

        MachineBooking mock = new MachineBooking();
        mock.setStatus(MachineBooking.MachineBookingStatus.ACTIVATION_PENDENT);

        return mock;
    }

    private void existsAnotherActiveBooking() throws MachineAlreadyBooked {
        MachineBookingQuery query = getActiveBookings();
        if (query.any()) {
            throw new MachineAlreadyBooked();
        }
    }

    private void create(User user) {
        this.booking = new MachineBooking();

        this.booking.setStatus(MachineBooking.MachineBookingStatus.IN_PROGRESS);
        this.booking.setMachine(this.machine);
        this.booking.setStartDate(new Date());
        this.booking.setUser(user);

        this.booking.save();
    }

    private void publishCreation() {
        String json = new MessageBuilder()
            .setMachineSerial(this.machine.getSerialNumber())
            .setBuildingId(this.machine.getBuilding().getId())
            .setTimestamp()
            .setResult(MachineUseResult.BOOKED)
            .setBlockedTime(BLOCKING_TIME_WAITING_FOR_CONFIRMATION)
            .setBookingId(this.booking.getPublicId())
            .build();

        new LMMQTTProducer()
            .onError((Exception ex) -> {
                cancel(ex.getMessage());
            })
            .run(this.machine.getBuilding().getId(), this.machine, json);
    }

    private void publishCancellation() {
        String json = new MessageBuilder()
            .setMachineSerial(this.machine.getSerialNumber())
            .setBuildingId(this.machine.getBuilding().getId())
            .setTimestamp()
            .setResult(MachineUseResult.BOOK_CANCELED)
            .setBookingId(this.booking.getPublicId())
            .build();

        new LMMQTTProducer().run(this.machine.getBuilding().getId(), this.machine, json);
    }
}
