package global;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import play.libs.F.Function;
import play.libs.F.Promise;
import play.mvc.Action;
import play.mvc.Http;
import play.mvc.Http.Context;
import play.mvc.Http.Request;
import play.mvc.Result;

public class LoggingMessageHandler extends Action<LoggingMessage> {

    private static final String[] SENSITIVE_FIELDS = new String[] {
        "password",
        "confirmPassword",
        "pwdConfirm",
    };
    private static final String SENSITIVE_FIELD_MASK = "******";

    private static final String[] ALLOWED_HEADERS = { Http.HeaderNames.USER_AGENT };

    @Override
    public Promise<Result> call(final Context ctx) throws Throwable {
        Promise<Result> result = null;
        Request request = ctx.request();
        try {
            loggingRequestInformation(request);

            if (this.delegate != null) result = this.delegate.call(ctx);
        } catch (APIException e) {
            result = ErrorMessageHandler.handleAPIException(e);
        } catch (Exception e) {
            if (e.getCause() instanceof APIException) {
                result = ErrorMessageHandler.handleAPIException((APIException) e.getCause());
            } else {
                result = ErrorMessageHandler.handleUnknownException(e);
            }
        }

        try {
            Promise<Result> mappedResult = result.flatMap(
                new Function<Result, Promise<Result>>() {
                    @Override
                    public Promise<Result> apply(Result result) throws Throwable {
                        loggingResponseInformation(request, result);
                        return Promise.<Result>pure(result);
                    }
                }
            );
            return mappedResult;
        } catch (Exception e) {
            handleError(request);
        }

        return result;
    }

    protected void loggingRequestInformation(Request request) {
        play.Logger.info(
            "{} {}{}",
            request.method(),
            request.path(),
            // conditional values
            getQueryString(request) + getBody(request) + getHeaders(request)
        );
    }

    protected void loggingResponseInformation(Request request, Result result) throws Exception {
        play.Logger.info(
            "{} {} - result={}",
            request.method(),
            request.path(),
            result.toScala().header().status()
        );
    }

    protected void handleError(Request request) {
        play.Logger.info(
            "{} {} - Error logging request information.",
            request.method(),
            request.path()
        );
    }

    private String getQueryString(Request request) {
        if (this.configuration.queryString()) {
            return " - params: " + getLoggableQueryString(request);
        }

        return StringUtils.EMPTY;
    }

    private String getBody(Request request) {
        if (this.configuration.body()) {
            ObjectNode body = null;
            ObjectNode tmpBody = (ObjectNode) request.body().asJson();
            if (tmpBody != null) {
                body = tmpBody.deepCopy();
                java.util.Iterator<Map.Entry<String, JsonNode>> it = body.fields();
                while (it.hasNext()) {
                    Map.Entry<String, JsonNode> node = it.next();
                    String key = node.getKey();
                    if (Arrays.stream(SENSITIVE_FIELDS).anyMatch(key::contains)) {
                        body.put(key, SENSITIVE_FIELD_MASK);
                    }
                }
            }

            return " - body: " + getLoggableBody(request);
        }

        return StringUtils.EMPTY;
    }

    private String getHeaders(Request request) {
        String[] headers = {};
        if (this.configuration.headers()) {
            headers = ArrayUtils.addAll(headers, ALLOWED_HEADERS);
        }
        if (this.configuration.auth()) {
            headers = ArrayUtils.addAll(headers, security.v1.Secured.LM_AUTH_HEADER);
        }

        if (ArrayUtils.isNotEmpty(headers)) {
            return " - headers: " + getLoggableHeaders(request, headers);
        }

        return StringUtils.EMPTY;
    }

    static String getLoggableQueryString(Request request) {
        return flatAndMaskSensitiveData(request.queryString());
    }

    static String getLoggableBody(Request request) {
        ObjectNode body = null;
        ObjectNode tmpBody = (ObjectNode) request.body().asJson();
        if (tmpBody != null) {
            body = tmpBody.deepCopy();
            java.util.Iterator<Map.Entry<String, JsonNode>> it = body.fields();
            while (it.hasNext()) {
                Map.Entry<String, JsonNode> node = it.next();
                String key = node.getKey();
                if (Arrays.stream(SENSITIVE_FIELDS).anyMatch(key::contains)) {
                    body.put(key, SENSITIVE_FIELD_MASK);
                }
            }
        }

        return body == null ? "{}" : body.toString();
    }

    static String getLoggableHeaders(Request request, String[] headers) {
        return flatAndFilterAllowedKeysData(request.headers(), headers);
    }

    private static String flatAndMaskSensitiveData(Map<String, String[]> data) {
        return flatData(
            data,
            (key, value) ->
                key +
                ": " +
                (
                    Arrays.stream(SENSITIVE_FIELDS).anyMatch(key::contains)
                        ? SENSITIVE_FIELD_MASK
                        : value
                )
        );
    }

    private static String flatAndFilterAllowedKeysData(
        Map<String, String[]> data,
        String... allowedKeys
    ) {
        return flatData(
            data,
            (key, value) ->
                Arrays.asList(allowedKeys).contains(key) ? key + ": " + value : StringUtils.EMPTY
        );
    }

    private static String flatData(
        Map<String, String[]> data,
        java.util.function.BiFunction<String, String, String> valueModifier
    ) {
        return data
            .entrySet()
            .stream()
            .map(e -> valueModifier.apply(e.getKey(), String.join(";", e.getValue())))
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.joining(", ", "{", "}"));
    }
}
