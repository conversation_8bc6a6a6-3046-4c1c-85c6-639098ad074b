package domains.payment_gateways.serializers.bancard;

import global.APIException;
import global.APIException.APIErrors;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import org.json.JSONException;
import org.json.JSONObject;

public class BancardRequest {

    public enum OperationType {
        SingleBuy,
        SingleBuyConfirm,
        SingleBuyGetConfirmation,
        SingleBuyRollback,
    }

    private String publicKey;
    private BancardOperation operation;

    private BancardRequest() {}

    public BancardRequest(
        String publicKey,
        String privateKey,
        String shopProcessId,
        double amount,
        String currency,
        String description,
        String returnUrl,
        String cancelUrl,
        OperationType type
    ) throws Exception {
        this.publicKey = publicKey;
        this.operation =
            new BancardOperation(
                privateKey,
                shopProcessId,
                amount,
                currency,
                description,
                returnUrl,
                cancelUrl,
                type
            );
    }

    public BancardRequest(
        String publicKey,
        String privateKey,
        String shopProcessId,
        OperationType type
    ) throws Exception {
        this.publicKey = publicKey;
        this.operation = new BancardOperation(privateKey, shopProcessId, type);
    }

    public JSONObject toJSON() throws APIException {
        JSONObject json = new JSONObject();

        try {
            json.put("public_key", this.publicKey);
            json.put("operation", this.operation.toJSON());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing BancardRequest : " + e.getMessage());
        }

        return json;
    }

    public class BancardOperation {

        // md5 de la petición
        private String token;
        // identificador de la compra
        private String shopProcessId;
        // Importe en guaraníes. Decimal (15,2), - separador decimal '.'
        private double amount;
        // Tipo de Moneda. length == 3, value == PYG
        private String currency; //
        // Descripción del pago, para mostrar al usuario. length <= 20
        private String description;
        // URL a donde se enviará al usuario al realizar el pago. Tener en cuenta que,
        // si la tarjeta es rechazada, también se le redirigirá a esta URL.
        private String returnUrl;
        // URL a donde se enviará al usuario al cancelar el pago. Opcional, se usará
        // return_url por defecto
        private String cancelUrl;

        private BancardOperation() {}

        public BancardOperation(
            String privateKey,
            String shopProcessId,
            double amount,
            String currency,
            String description,
            String returnUrl,
            String cancelUrl,
            BancardRequest.OperationType type
        ) throws Exception {
            if (!currency.equals("PYG")) throw APIException
                .raise(APIErrors.MISSING_PARAMETERS)
                .setDetailMessage("BancardOperation parameter error: currency. valor: " + currency);
            if (description.length() > 20) throw APIException
                .raise(APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(
                    "BancardOperation parameter error: description. Largo: " + description.length()
                );
            if (returnUrl.length() < 1) throw APIException
                .raise(APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(
                    "BancardOperation parameter error: returnUrl. Largo: " + returnUrl.length()
                );

            this.shopProcessId = shopProcessId;
            this.amount = amount;
            this.currency = currency;
            this.description = description;
            this.returnUrl = returnUrl;
            this.cancelUrl = cancelUrl;

            setToken(privateKey, type);
        }

        public BancardOperation(
            String privateKey,
            String shopProcessId,
            BancardRequest.OperationType type
        ) throws Exception {
            this.shopProcessId = shopProcessId;

            setToken(privateKey, type);
        }

        public JSONObject toJSON() throws JSONException {
            JSONObject json = new JSONObject();

            json.put("token", this.token);
            json.put("shop_process_id", this.shopProcessId);
            json.put("amount", amountDecimalFormat());
            json.put("currency", this.currency);
            json.put("description", this.description);
            json.put("return_url", this.returnUrl);
            json.put("cancel_url", this.cancelUrl);

            return json;
        }

        private void setToken(String privateKey, BancardRequest.OperationType type)
            throws NoSuchAlgorithmException {
            switch (type) {
                case SingleBuy:
                    this.setTokenSingleBuy(privateKey);
                    break;
                case SingleBuyConfirm:
                    this.setTokenSingleBuyConfirm(privateKey);
                    break;
                case SingleBuyGetConfirmation:
                    this.setTokenSingleBuyGetConfirmation(privateKey);
                    break;
                case SingleBuyRollback:
                    this.setTokenSingleBuyRollback(privateKey);
                    break;
            }
        }

        private void setTokenSingleBuy(String privateKey) throws NoSuchAlgorithmException {
            String input =
                privateKey + this.shopProcessId + this.amountDecimalFormat() + this.currency;
            this.token = getMd5(input);
        }

        private void setTokenSingleBuyConfirm(String privateKey) throws NoSuchAlgorithmException {
            String input =
                privateKey +
                this.shopProcessId +
                "confirm" +
                this.amountDecimalFormat() +
                this.currency;
            this.token = getMd5(input);
        }

        private void setTokenSingleBuyGetConfirmation(String privateKey)
            throws NoSuchAlgorithmException {
            String input = privateKey + this.shopProcessId + "get_confirmation";
            this.token = getMd5(input);
        }

        private void setTokenSingleBuyRollback(String privateKey) throws NoSuchAlgorithmException {
            String input = privateKey + this.shopProcessId + "rollback" + "0.00";
            this.token = getMd5(input);
        }

        private String amountDecimalFormat() {
            DecimalFormat df = new DecimalFormat("0.00");
            return df.format(this.amount);
        }

        private String getMd5(String input) throws NoSuchAlgorithmException {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            BigInteger no = new BigInteger(1, messageDigest);
            String hashtext = no.toString(16);
            while (hashtext.length() < 32) hashtext = "0" + hashtext;

            return hashtext;
        }
    }
}
