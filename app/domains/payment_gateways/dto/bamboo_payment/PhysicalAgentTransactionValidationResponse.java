package domains.payment_gateways.dto.bamboo_payment;

import domains.payment_gateways.services.bamboo_payment.BaseBambooPaymentTransactionFlow;
import java.util.ArrayList;
import java.util.List;
import models.Currency;

public class PhysicalAgentTransactionValidationResponse {

    /**
     * OK, la operación fue correctamente ejecutada.
     */
    protected static final int OK_STATUS = 0;
    /**
     * Ocurrió un error interno en la aplicación.
     */
    protected static final int UNEXPECTED_ERROR_STATUS = -1;
    /**
     * Usuario y password inválidos
     */
    protected static final int INVALID_CREDENTIALS_ERROR_STATUS = 1;
    /**
     * No hay transacciones pendientes de pago
     */
    protected static final int NO_PENDING_TRANSACTIONS_ERROR_STATUS = 2;
    /**
     * Pago inconsistente. La validación de datos del pago arrojó inconsistencias.
     */
    protected static final int PAYMENT_INCONSISTENCY_ERROR_STATUS = 100;
    /**
     * No coincide la moneda indicada con la moneda de la transacción
     */
    protected static final int WRONG_DETAILED_MONEY_ERROR_STATUS = 101;
    /**
     * No coincide el importe indicado con el de la transacción
     */
    protected static final int TOTAL_DIFFERENCE_ERROR_STATUS = 102;
    /**
     * No coincide el tipo de documento indicado
     */
    protected static final int UNEXPECTED_DOCUMENT_TYPE_ERROR_STATUS = 103;
    /**
     * No se pueden realizar pagos de distinta moneda
     */
    protected static final int DIFFERENT_MONEY_ERROR_STATUS = 104;
    /**
     * El importe total de las transacciones es distinto al importe indicado
     */
    protected static final int DIFFERENT_TOTALS_ERROR_STATUS = 105;
    /**
     * No se indicaron transacciones a pagar
     */
    protected static final int NO_TRANSACTIONS_INDICATED_ERROR_STATUS = 106;
    /**
     * La transacción ya se encuentra paga.
     */
    protected static final int ALREADY_PAID_TRANSACTION_ERROR_STATUS = 107;
    /**
     * No se puede procesar anulación. El tiempo máximo para realizar anulaciones ya expiró.
     */
    protected static final int OUT_OF_TIME_CANCELLATION_ERROR_STATUS = 200;
    /**
     * No hay transacciones para anular
     */
    protected static final int NOT_FOUND_TRANSACTION_TO_CANCEL_ERROR_STATUS = 201;

    private int status;
    private String statusDescription;
    private List<PhysicalAgentTransactionPaymentDetail> pendingPayments;

    public int getStatus() {
        return status;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public List<PhysicalAgentTransactionPaymentDetail> getPendingPayments() {
        return pendingPayments;
    }

    public void setInvalidCredentialsError() {
        status = INVALID_CREDENTIALS_ERROR_STATUS;
        statusDescription = "Usuario y password inválidos";
        pendingPayments = new ArrayList<>();
    }

    public void setPaymentInconsistencyError() {
        status = PAYMENT_INCONSISTENCY_ERROR_STATUS;
        statusDescription =
            "Pago inconsistente. La validación de datos del pago arrojó inconsistencias.";
        pendingPayments = new ArrayList<>();
    }

    public void setUnexpectedError() {
        status = UNEXPECTED_ERROR_STATUS;
        statusDescription = "Ocurrió un error interno en la aplicación.";
        pendingPayments = new ArrayList<>();
    }

    public void setCreationValidated() {
        status = OK_STATUS;
        statusDescription = "OK, la operación fue correctamente ejecutada.";
        pendingPayments = new ArrayList<>();

        PhysicalAgentTransactionPaymentDetail payment = new PhysicalAgentTransactionPaymentDetail();

        // these attrs values are defined by spec
        payment.setItemId(BaseBambooPaymentTransactionFlow.ITEM_ID);
        payment.setDescription("Carga de Saldo"); // TODO: check
        payment.setTotal(0);
        payment.setCurrency(Currency.UYU);
        payment.setFinalConsumer(false);
        payment.setTotalPlusTaxes(0);
        payment.setBillNumber("");

        pendingPayments.add(payment);
    }

    public void setConfirmationOrRejectionValidated() {
        status = OK_STATUS;
        statusDescription = "OK, la operación fue correctamente ejecutada.";
    }

    public void setInvalidDocumentTypeError() {
        status = UNEXPECTED_DOCUMENT_TYPE_ERROR_STATUS;
        statusDescription = "No coincide el tipo de documento indicado";
        pendingPayments = new ArrayList<>();
    }

    public void setDifferenceBetweenTotalsError() {
        status = TOTAL_DIFFERENCE_ERROR_STATUS;
        statusDescription = "El importe total de las transacciones es distinto al importe indicado";
    }

    public void setAlreadyPaidTransactionError() {
        status = ALREADY_PAID_TRANSACTION_ERROR_STATUS;
        statusDescription = "La transacción ya se encuentra paga.";
    }

    public void setNotFoundTransaction() {
        status = NOT_FOUND_TRANSACTION_TO_CANCEL_ERROR_STATUS;
        statusDescription = "No hay transacciones para anular";
    }

    public void setCannotCancelTransaction() {
        status = OUT_OF_TIME_CANCELLATION_ERROR_STATUS;
        statusDescription =
            "No se puede procesar anulación. El tiempo máximo para realizar anulaciones ya expiró.";
    }
}
