package domains.payment_gateways.dto.bamboo_payment;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Card;
import utils.StringHelper;

public class PhysicalAgentTransactionValidationParameters extends dto.JsonBodyActionParameters {

    /**
     * Cédula de identidad uruguaya
     */
    protected static final int CI_UY_DOCUMENT_TYPE = 1;
    /**
     * RUT empresa uruguaya
     */
    protected static final int RUT_UY_DOCUMENT_TYPE = 2;
    /**
     * Documento extranjero (persona física)
     */
    protected static final int FOREIGN_ID_DOCUMENT_TYPE = 3;
    /**
     * Código de cuenta de Banred.
     * Este valor lo ingresa el usuario al asociar una empresa como servicio asociado
     */
    protected static final int BANRED_ID_DOCUMENT_TYPE = 4;
    /**
     * Referencia genérica / Identificador externo
     */
    protected static final int INTERNAL_REFERENCE_DOCUMENT_TYPE = 5;

    /**
     * Identifica operaciones desde Abitab
     */
    protected static final String ABITAB_AGENT = "ABITAB";
    /**
     * Identifica operaciones desde RedPagos
     */
    protected static final String REDPAGOS_AGENT = "REDPAGOS";
    /**
     * Identifica operaciones desde Banred
     */
    protected static final String BANRED_AGENT = "BANRED";

    protected String user;
    protected String password;
    protected int documentType;
    protected String uid;
    protected String agent;
    protected Card card;

    public PhysicalAgentTransactionValidationParameters(JsonNode body) throws APIException {
        user = safeString("username", body, "");
        password = safeString("password", body, "");
        documentType = safeInt("tipoDocumento", body, 0);
        uid = safeString("documento", body, ""); //  - UID indicada por el usuario
        agent = safeString("redFisica", body, "");
    }

    @Override
    public PhysicalAgentTransactionValidationParameters validate() throws APIException {
        if (StringHelper.isBlank(user) || StringHelper.isBlank(password)) {
            throw APIException.raise(APIException.APIErrors.UNAUTHORIZED);
        }

        if (documentType != INTERNAL_REFERENCE_DOCUMENT_TYPE) {
            throw APIException
                .raise(APIException.APIErrors.INVALID_DOCUMENT_TYPE)
                .setDetailMessage(
                    "The \"tipoDocumento\" provided is wrong. Expected value: " +
                    INTERNAL_REFERENCE_DOCUMENT_TYPE
                );
        }

        if (!agent.equals(REDPAGOS_AGENT)) {
            throw APIException
                .raise(APIException.APIErrors.INVALID_AGENT)
                .setDetailMessage(
                    "The \"redFisica\" provided is wrong. Expected value: " + REDPAGOS_AGENT
                );
        }

        if (StringHelper.isBlank(uid)) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage("The \"documento\" provided is empty.");
        }

        this.uid = utils.CardHelper.sanitizeUid(uid);
        this.card = Card.findByUID(uid);
        if (card == null) {
            throw APIException.raise(APIException.APIErrors.CARD_NOT_FOUND);
        }
        if (!card.getContractType().equals(Card.ContractType.PREPAID)) {
            throw APIException.raise(APIException.APIErrors.CARD_NOT_PREPAID);
        }
        if (!this.card.isEnabled()) {
            throw APIException.raise(APIException.APIErrors.INACTIVE_CARD);
        }

        return this;
    }

    public String getUser() {
        return user;
    }

    public String getPassword() {
        return password;
    }
}
