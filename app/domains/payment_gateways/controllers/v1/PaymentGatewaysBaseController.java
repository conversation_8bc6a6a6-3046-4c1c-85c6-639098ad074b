package domains.payment_gateways.controllers.v1;

import controllers.AbstractController;
import models.Transaction;
import queries.transactions.TransactionQuery;

@global.ErrorMessage
@global.LoggingMessage
public class PaymentGatewaysBaseController extends AbstractController {

    protected Transaction getTransaction(String publicId) {
        return new TransactionQuery().filterByPublicId(publicId).single();
    }
}
