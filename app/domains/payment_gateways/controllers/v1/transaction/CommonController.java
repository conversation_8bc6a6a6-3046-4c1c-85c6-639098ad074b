package domains.payment_gateways.controllers.v1.transaction;

import com.play4jpa.jpa.db.Tx;
import domains.payment_gateways.services.ITransactionFlow;
import global.APIException;
import play.libs.F;
import play.mvc.Result;
import play.twirl.api.Html;

/**
 * Shared actions between different providers
 */
public class CommonController extends TransactionBaseController {

    /**
     * GET  /api/v1/transaction/reject
     *
     * Calls when the failure backUrl is executed.
     * It is shared by multiple payment gateways.
     *
     * Mercado Pago: then we would inform the reason through reporting by IPN
     *
     * @param customerTransactionId Transaction publicId
     * @param origin provider identification
     *
     */
    @Tx
    public F.Promise<Result> reject(String customerTransactionId, String origin)
        throws APIException {
        play.Logger.debug(
            "## rejectTransaction ## customerTransactionId: {} - origin: {}",
            customerTransactionId,
            origin
        );

        ITransactionFlow service = getServiceInstanceByProvider(
            origin,
            BANCARD_PROVIDER,
            MERCADO_PAGO_PROVIDER
        );
        try {
            Html response = service.rejectTransaction(customerTransactionId);

            return F.Promise.<Result>pure(ok(response));
        } catch (Exception e) {
            service.handleException(e);
        }

        return unreachableReturn();
    }
}
