package domains.payment_gateways.controllers.v1.transaction;

import com.play4jpa.jpa.db.Tx;
import domains.payment_gateways.dto.back_office.BackOfficeTransactionParameters;
import domains.payment_gateways.services.back_office.ManualTransactionFlow;
import global.APIException;
import global.BackOfficeCop;
import play.libs.F;
import play.mvc.Result;
import policies.actions.Policy;
import policies.transactions.TransactionPolicy;

@Policy(TransactionPolicy.class)
public class BackOfficeController extends TransactionBaseController {

    /**
     * POST /api/v1/transaction/back-office
     */
    @Tx
    @BackOfficeCop
    public F.Promise<Result> create() throws APIException {
        TransactionPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        BackOfficeTransactionParameters parameters = new BackOfficeTransactionParameters(body())
            .validate();

        ManualTransactionFlow service = new ManualTransactionFlow(parameters);
        service.transact();

        return F.Promise.<Result>pure(ok());
    }
}
