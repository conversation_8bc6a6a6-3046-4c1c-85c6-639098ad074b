package domains.payment_gateways.services.trans_act;

import domains.activations.MachineActivationService;
import domains.billing.exceptions.BillingException;
import domains.billing.exceptions.UnableToBillException;
import domains.billing.invoicing.InvoicingService;
import domains.billing.invoicing.InvoicingServiceFactory;
import domains.billing.invoicing.exceptions.InvoicingException;
import domains.billing.invoicing.types.BookingResult;
import domains.billing.invoicing.types.InvoicingResult;
import domains.payment_gateways.dto.trans_act.CreateTransactionParameters;
import domains.payment_gateways.serializers.trans_act.RespuestaConsultarTransaccionSerializer;
import domains.payment_gateways.services.BaseTransactionFlow;
import global.APIException;
import global.APIException.APIErrors;
import java.rmi.RemoteException;
import models.*;
import org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.tempuri.ITarjetasTransaccion_400Proxy;
import queries.machines.MachineQuery;
import queries.mappings.TotemMachineQuery;
import queries.mappings.TotemPosTerminalQuery;
import utils.ApplicationConfiguration;

public class TransActTransactionFlow extends BaseTransactionFlow {

    private static String MONEDA_ISO_UY = "0858";
    private static String MONEDA_ISO_USD = "0840";
    private static String OPERACION_VENTA = "VTA";
    private static String OPERACION_DEVOLUCION = "DEV";
    private static Integer TARJETA_ID_TODAS = 0;
    private static Integer EMISOR_ID_TODOS = 0;
    private static String ORIGIN = "TransAct";
    private boolean isThirdPartyLaundromat = false;

    public String createCreditTransaction(CreateTransactionParameters params) throws APIException {
        super.createCreditTransaction();
        Bill bill = null;

        try {
            Machine machine = new MachineQuery()
                .filterBySerialNumber(params.getMachineSerial())
                .single();

            if (machine == null || machine.getBuilding() == null) {
                throw APIException.raise(APIErrors.MACHINE_NOT_FOUND);
            }

            logger("CREATE_TRANSACTION >>> TransActTransactionFlow");

            if (
                machine.getState() != Part.PartState.NEW &&
                machine.getState() != Part.PartState.ACTIVE
            ) {
                throw APIException.raise(APIErrors.MACHINE_DISABLED);
            }

            if (
                !MachineActivationService
                    .instance()
                    .canActivateMachine(machine, params.getChannel(), params.getUser())
            ) {
                throw APIException.raise(APIErrors.MACHINE_BUSY);
            }

            Building building = machine.getBuilding();
            if (building == null) {
                throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
            }

            this.isThirdPartyLaundromat =
                building.getBuildingType() == BuildingType.THIRD_PARTY_LAUNDROMAT;

            Rate rate = machine.getMachineRate();

            Double amount = rate.getPriceCustomer();

            if (amount == null || amount == 0.0) {
                throw APIException.raise(APIErrors.RATE_NOT_FOUND);
            }

            Transaction transaction = new Transaction(
                Currency.UYU,
                amount,
                params.getMachineSerial(),
                machine,
                ORIGIN
            );
            if (params.requiresCustomerInfo()) {
                transaction.setRut(params.getCustomerRut());
                transaction.setName(params.getCustomerName());
                transaction.setAddress(params.getCustomerAddress());
            }
            transaction.save();

            if (rate.isSplittingApplicable()) {
                double lavomatComission = lavomatCommission(rate, amount);
                if (lavomatComission > 0) {
                    transaction.setComission(lavomatComission);
                }
            }

            transaction.setReasonType(Transaction.ReasonType.CREDIT);
            transaction.update();

            logger("CREATE_TRANSACTION >>> CREATING BILL");

            bill =
                // DECLAIMER:
                // it is known that all TransAct transactions come from the TOTEM,
                // so, it needs an associated a recipient
                createBillForReservationWithAssociatedRecipient(transaction, building);

            InvoicingService invoicingService = InvoicingServiceFactory.createService(bill);

            BookingResult result = invoicingService.bookInvoiceNumber(bill);
            this.billingService.updateBillPeriodEnd(bill, result);

            logger(
                "CREATE_TRANSACTION >>> TransActTransactionFlow >> Reserva " +
                result.getInvoiceNumber()
            );

            transaction.setBill(bill);
            transaction.update();

            String posTerminalCode = getPosTerminalCode(machine, building);

            ITarjetasTransaccion_400RespuestaPostearTransaccion postedTransaction = postTransaccion(
                posTerminalCode,
                transaction.getRut(),
                transaction.getAmount(),
                (double) result.getInvoiceNumber()
            );

            String token = "";
            if (postedTransaction.getResp_CodigoRespuesta() == 0) {
                token = postedTransaction.getTokenNro();
            } else {
                logger("CREATE_TRANSACTION >>> ERROR: " + postedTransaction.getResp_MensajeError());
                transaction.setAuthorizationresultmessage("Transacción rechazada por TransAct");
                transaction.setErrormessage(postedTransaction.getResp_MensajeError());
                transaction.setErrorcode(Transaction.ErrorCode.ERROR);
                transaction.setAuthorizationresult(Transaction.AuthorizationResult.DENIED);
                transaction.update();
            }

            JSONObject response = new JSONObject();
            response.put("Transaction_token", token);
            response.put("Transaction_id", transaction.getPublicId());
            response.put("Transaction_amount", transaction.getAmount());
            response.put("Transaction_currency", transaction.getCurrency());
            return response.toString();
        } catch (BillingException | InvoicingException | RemoteException | JSONException e) {
            if (e instanceof InvoicingException && bill != null) {
                bill.setState(Bill.BillState.ERROR_DGI);
                bill.update();
            } else {
                e.printStackTrace();
            }
            throw APIException
                .raise(APIErrors.INTERNAL_SERVER_ERROR, e)
                .withParentDetailMessage()
                .withParentStackTrace();
        }
    }

    private String getPosTerminalCode(Machine machine, Building building) {
        TotemUserMachineMap totemUserMachineMap = new TotemMachineQuery()
            .filterByMachineId(machine.getId())
            .single();
        if (totemUserMachineMap != null) {
            TotemUserPosTerminalMap totemUserPosTerminalMap = new TotemPosTerminalQuery()
                .filterByTotemUserId(totemUserMachineMap.getTotemUser().getId())
                .single();
            if (totemUserPosTerminalMap != null) {
                return totemUserPosTerminalMap.getPosTerminalCode();
            }
        }

        if (building.getTransActTerminalCode() != null) {
            return building.getTransActTerminalCode();
        } else {
            return this.isThirdPartyLaundromat
                ? ApplicationConfiguration.getTransactLavamarTermcode()
                : ApplicationConfiguration.getTransactLavomatTermcode();
        }
    }

    private ITarjetasTransaccion_400RespuestaPostearTransaccion postTransaccion(
        String posTerminalCode,
        String billRut,
        Double billAmount,
        Double billNumber
    ) throws RemoteException {
        logger("CREATE_TRANSACTION >>> SENDING POST TRANSACTION");

        ITarjetasTransaccion_400Transaccion transaction = new ITarjetasTransaccion_400Transaccion();

        // Código de empresa TransAct, este código debe ser solicitado a TransAct para
        // cada cliente
        transaction.setEmpCod(getBusinessCode());
        transaction.setEmpHASH(getBusinessHash());
        // Código de terminal, este código debe ser solicitado a TransAct para cada
        // cliente
        transaction.setTermCod(posTerminalCode);

        // Indica si cliente es consumidor final, si tiene RUT enviar en FALSE
        transaction.setFacturaConsumidorFinal(billRut == null || billRut.isEmpty());

        // Monto de la factura (Difiere del Monto de la Transacción cuando Factura se
        // paga con varios medios de Pago)
        double amount = convertToTransActFormat(billAmount);
        transaction.setFacturaMonto(amount);
        // Monto de IVA de la factura
        double facturaMontoIVA = amount * 0.22;
        transaction.setFacturaMontoIVA(facturaMontoIVA);
        // Monto gravado de la factura
        transaction.setFacturaMontoGravado(amount - facturaMontoIVA);
        // Número de factura (requerido)
        transaction.setFacturaNro(billNumber);
        // Código de moneda
        transaction.setMonedaISO(MONEDA_ISO_UY);
        // Monto de transacción
        transaction.setMonto(amount);
        // Monto de CashBack
        transaction.setMontoCashBack(0.0);
        // Monto de propina
        transaction.setMontoPropina(0.0);
        transaction.setOperacion(OPERACION_VENTA);
        // Id de tarjeta cuya función es obligar al cajero a pasar una tarjeta
        // específica
        transaction.setTarjetaId(TARJETA_ID_TODAS);
        // Id de emisor, cuya función es obligar al cajero a pasar una tarjeta de un
        // emisor(banco) específico
        transaction.setEmisorId(EMISOR_ID_TODOS);
        // transaction.setTarjetaTipo(null); // CRE=Obligar a que sea Crédito,
        // DEB=Obligar a que sea Débito, No setear=Permitir todas.

        ITarjetasTransaccion_400TransaccionExtendida extendedTransaction = new ITarjetasTransaccion_400TransaccionExtendida();
        // Cantidad de cuotas
        extendedTransaction.setCuotas(1);
        transaction.setExtendida(extendedTransaction);

        ITarjetasTransaccion_400Configuracion configuration = new ITarjetasTransaccion_400Configuracion();
        // Setear en TRUE cuando no se cuenta con pos fisico para las pruebas
        configuration.setModoEmulacion(isEmulationMode());
        transaction.setConfiguracion(configuration);

        ITarjetasTransaccion_400Proxy proxy = new ITarjetasTransaccion_400Proxy();
        return proxy.postearTransaccion(transaction);
    }

    private String getBusinessCode() {
        return this.isThirdPartyLaundromat
            ? ApplicationConfiguration.getTransactLavamarEmpcode()
            : ApplicationConfiguration.getTransactLavomatEmpcode();
    }

    private String getBusinessHash() {
        return this.isThirdPartyLaundromat
            ? ApplicationConfiguration.getTransactLavamarEmphash()
            : ApplicationConfiguration.getTransactLavomatEmphash();
    }

    private Boolean isEmulationMode() {
        return this.isThirdPartyLaundromat
            ? ApplicationConfiguration.getTransactLavamarEmulacion()
            : ApplicationConfiguration.getTransactLavomatEmulacion();
    }

    private Double convertToTransActFormat(Double number) {
        String value = String.format("%1.2f", number);
        return Double.valueOf(value) * 100;
    }

    public byte[] confirmTransaction(String id) throws Exception {
        logger("CONFIRM_TRANSACTION >>> TransActTransactionFlow");
        logger("CONFIRM_TRANSACTION >>> TRX_ID: " + id);

        Transaction transaction = getTransaction(id);
        Bill bill = transaction.getBill();

        if (
            Transaction.AuthorizationResult.AUTHORIZED.equals(transaction.getAuthorizationresult())
        ) {
            throw APIException.raise(APIErrors.TRANSACTION_ALREADY_CONFIRMED);
        } else {
            InvoicingService invoicingService = InvoicingServiceFactory.createService(bill);
            try {
                InvoicingResult result = invoicingService.confirmBookedNumber(bill);

                this.billingService.updateBillNumbering(bill, result);
                this.billingService.updateBillState(bill, Bill.BillState.SENT);

                if (result.getInvoiceNumber().equals(bill.getNumber())) {
                    transaction.confirm();
                    transaction.update();

                    updateCards(transaction, transaction.getAmount());

                    logger("CONFIRM_TRANSACTION >>> La transaccion ha sido confirmada con exito");
                }

                if (transaction.getComission() > 0) {
                    double billAmountWithoutIVA =
                        transaction.getAmount() - transaction.getComission();

                    createBillTaxFreeThroughKiosk(
                        billAmountWithoutIVA,
                        transaction.getCurrency().getIsoNumber(),
                        transaction
                    );
                }
            } catch (InvoicingException e) {
                loggerError("Error occurred while confirming the invoice.", e);
                throw new UnableToBillException();
            }
        }

        byte[] pdf = generateBillPDF(bill);
        if (pdf == null) {
            logger(
                "CONFIRM_TRANSACTION >>> No se pudo obtener el PDF correspondiente a la factura: serie: {} - number: {} - tipoDoc: {}",
                bill.getSerie(),
                bill.getNumber(),
                bill.getTipoDoc()
            );
            pdf = new byte[0];
        }

        return pdf;
    }

    public void rejectTransaction(String id, String token, String message) throws Exception {
        logger("REJECT_TRANSACTION >>> TransActTransactionFlow");
        logger("REJECT_TRANSACTION >>> TRX_ID: " + id);

        ITarjetasTransaccion_400RespuestaCancelarTransaccion canceledTransaction = cancelTransaction(
            token
        );

        int transactAnswerCode = canceledTransaction.getResp_CodigoRespuesta();
        logger("REJECT_TRANSACTION >>> Respuesta - CodigoRespuesta: " + transactAnswerCode);
        String transactErrorMessage = canceledTransaction.getResp_MensajeError();
        logger("REJECT_TRANSACTION >>> Respuesta - MensajeError: " + transactErrorMessage);
        if (transactAnswerCode != 0) {
            throw APIException
                .raise(APIErrors.TRANSACTION_CAN_NOT_BE_CANCELED)
                .setDetailMessage(transactErrorMessage);
        }

        Transaction transaction = getTransaction(id);

        transaction.setErrorcode(Transaction.ErrorCode.INCOMPLETE);
        transaction.setAuthorizationresult(Transaction.AuthorizationResult.OTHER);
        transaction.setAuthorizationresultmessage("Transacción cancelada por petición");
        String errorMessage = "Transacción cancelada por petición";
        if (message != null && !message.isEmpty()) {
            errorMessage += " - " + message;
        }
        if (transactErrorMessage != null && !transactErrorMessage.isEmpty()) {
            errorMessage += " - " + transactErrorMessage;
        }
        transaction.setErrormessage(errorMessage);
        transaction.update();

        Bill bill = transaction.getBill();

        InvoicingService invoicingService = InvoicingServiceFactory.createService(bill);
        try {
            invoicingService.cancelBookedNumber(bill);
        } catch (InvoicingException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.INTERNAL_SERVER_ERROR, e)
                .withParentDetailMessage()
                .withParentStackTrace();
        }

        this.billingService.updateBillState(bill, Bill.BillState.CANCELLED);

        logger(
            "REJECT_TRANSACTION >>> La transaccion ha sido rechazada con exito y el numero desecahdo"
        );
    }

    private ITarjetasTransaccion_400RespuestaCancelarTransaccion cancelTransaction(String token)
        throws RemoteException {
        logger("REJECT_TRANSACTION >>> SENDING CANCEL TRANSACTION");

        ITarjetasTransaccion_400Proxy proxy = new ITarjetasTransaccion_400Proxy();
        return proxy.cancelarTransaccion(token);
    }

    public String statusTransaction(String token) throws Exception {
        logger("STATUS_TRANSACTION >>> TransActTransactionFlow");

        ITarjetasTransaccion_400RespuestaConsultarTransaccion queryTransaction = queryTransaction(
            token
        );

        return RespuestaConsultarTransaccionSerializer.queryToJson(queryTransaction).toString();
    }

    private ITarjetasTransaccion_400RespuestaConsultarTransaccion queryTransaction(String token)
        throws RemoteException {
        logger("STATUS_TRANSACTION >>> SENDING QUERY TRANSACTION");

        ITarjetasTransaccion_400Proxy proxy = new ITarjetasTransaccion_400Proxy();
        return proxy.consultarTransaccion(token);
    }
}
