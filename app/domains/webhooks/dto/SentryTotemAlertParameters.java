package domains.webhooks.dto;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;

public class SentryTotemAlertParameters extends dto.ActionParameters {

    protected static final String MAIN_PARAM = "closureData";
    protected static final String ALERT_PARAM = "triggered_rule";
    protected static final String EVENT_PARAM = "event";
    protected static final String EVENT_LEVEL_PARAM = "level";
    protected static final String EVENT_TITLE_PARAM = "title";
    protected static final String EVENT_WEB_URL_PARAM = "web_url";

    protected String alert;
    protected String errorLevel;
    protected String errorTitle;
    protected String errorUrl;

    public SentryTotemAlertParameters(JsonNode body) {
        JsonNode data = body.get(MAIN_PARAM);
        if (data != null) {
            this.alert = data.get(ALERT_PARAM).asText();

            JsonNode event = data.get(EVENT_PARAM);
            if (event != null) {
                this.errorLevel = event.get(EVENT_LEVEL_PARAM).asText();
                this.errorTitle = event.get(EVENT_TITLE_PARAM).asText();
                this.errorUrl = event.get(EVENT_WEB_URL_PARAM).asText();
            }
        }
    }

    @Override
    public SentryTotemAlertParameters validate() throws APIException {
        return this;
    }

    public String getAlert() {
        return this.alert;
    }

    public String getErrorLevel() {
        return this.errorLevel;
    }

    public String getErrorTitle() {
        return this.errorTitle;
    }

    public String getErrorUrl() {
        return this.errorUrl;
    }
}
