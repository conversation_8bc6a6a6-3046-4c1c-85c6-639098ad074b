package domains.assistant.dto.activation;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.exceptions.BuildingNotFoundException;
import global.exceptions.CardNotFoundException;
import global.exceptions.MissingParametersException;
import global.exceptions.machines.MachineNotFoundException;
import models.Building;
import models.Card;
import models.Machine;
import models.User;
import org.apache.commons.lang3.StringUtils;
import queries.buildings.BuildingQuery;
import queries.cards.CardAssociatedToUserQuery;
import utils.CardHelper;

public class ActivationParameters extends dto.JsonBodyActionParameters {

    protected static final String BUILDING_SLUG_PARAM = "slug";
    protected static final String MACHINE_SORT_INDEX_PARAM = "index";
    protected static final String CARD_UID_PARAM = "uid";

    protected final User user;
    protected String buildingSlug;
    protected String cardUid;
    protected int machineSortIndex;

    protected Building building;
    protected Machine machine;
    protected Card card;

    public ActivationParameters(User user, JsonNode body) throws APIException {
        this.user = user;
        this.buildingSlug = safeString(BUILDING_SLUG_PARAM, body);
        this.cardUid = safeString(CARD_UID_PARAM, body);
        this.machineSortIndex = safeInt(MACHINE_SORT_INDEX_PARAM, body, 0);
    }

    @Override
    public ActivationParameters validate() throws APIException {
        if (
            this.user == null ||
            StringUtils.isBlank(this.buildingSlug) ||
            StringUtils.isBlank(this.cardUid) ||
            this.machineSortIndex == 0 ||
            this.machineSortIndex == Machine.DISABLED_SORT_INDEX
        ) {
            throw new MissingParametersException(
                BUILDING_SLUG_PARAM,
                MACHINE_SORT_INDEX_PARAM,
                CARD_UID_PARAM
            );
        }

        this.building = new BuildingQuery().filterBySlug(this.buildingSlug).single();
        if (this.building == null) {
            throw new BuildingNotFoundException();
        }

        this.machine =
            this.building.getMachines()
                .stream()
                .filter(m -> m.getSortIndex() == this.machineSortIndex)
                .findFirst()
                .orElse(null);
        if (this.machine == null) {
            throw new MachineNotFoundException();
        }

        String uid = CardHelper.sanitizeUid(this.cardUid);
        this.card =
            new CardAssociatedToUserQuery(this.user)
                .find()
                .stream()
                .filter(c -> c.getUuid().equals(uid))
                .findFirst()
                .orElse(null);
        if (this.card == null) {
            throw new CardNotFoundException();
        }

        return this;
    }

    public Card getCard() {
        return this.card;
    }

    public Machine getMachine() {
        return this.machine;
    }

    public Building getBuilding() {
        return this.building;
    }
}
