package domains.assistant.dto.buildings;

import global.APIException;
import models.Building;
import queries.buildings.BuildingQuery;

public class GetBuildingBySlugParameters extends dto.ActionParameters {

    protected final String slug;
    protected Building building;

    public GetBuildingBySlugParameters(String slug) {
        this.slug = slug;
    }

    public GetBuildingBySlugParameters validate() throws APIException {
        this.building = new BuildingQuery().filterBySlug(this.slug).single();

        if (this.building == null) {
            throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
        }

        return this;
    }

    public Building getBuilding() {
        return this.building;
    }
}
