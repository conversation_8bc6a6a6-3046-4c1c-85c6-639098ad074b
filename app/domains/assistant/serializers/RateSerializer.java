package domains.assistant.serializers;

import global.APIException;
import java.util.List;
import models.Rate;
import org.json.JSONException;
import org.json.JSONObject;
import serializers.BaseSerializer;

public class RateSerializer extends BaseSerializer<Rate> {

    public RateSerializer(Rate entity) {
        super(entity);
    }

    public RateSerializer(List<Rate> entities) {
        super(entities, "rates");
    }

    public JSONObject itemToJson(Rate rate, int level) throws APIException {
        JSONObject rateJson = new JSONObject();

        try {
            rateJson.put("name", rate.getName());

            if (level >= 1) {
                rateJson.put("priceCustomer", rate.getPriceCustomer());
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing rate to the bot: " + e.getMessage());
        }

        return rateJson;
    }
}
