package domains.rpi.serializers.machines;

import global.APIException;
import models.Firmware;
import models.Machine;
import org.json.JSONException;
import org.json.JSONObject;

public class AliveSerializer {

    public static JSONObject itemToJson(
        Machine mainMachine,
        Machine secondMachine,
        Firmware firmware
    ) throws APIException {
        JSONObject json = new JSONObject();

        try {
            json.put(
                "rate1",
                mainMachine.getMachineRate() != null
                    ? mainMachine.getMachineRate().getPriceCustomer()
                    : 0
            );

            if (secondMachine != null) {
                json.put(
                    "rate2",
                    secondMachine.getMachineRate() != null
                        ? secondMachine.getMachineRate().getPriceCustomer()
                        : 0
                );
            }

            if (firmware != null) {
                json.put("frame_type", "FIRMWARE_UPGRADE");
                json.put("fw_url", firmware.getUrl());
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIException.APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing alive response : " + e.getMessage());
        }

        return json;
    }
}
