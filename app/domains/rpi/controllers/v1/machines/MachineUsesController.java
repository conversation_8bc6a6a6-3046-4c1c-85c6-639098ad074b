package domains.rpi.controllers.v1.machines;

import com.play4jpa.jpa.db.Tx;
import domains.rpi.controllers.v1.RpiBaseController;
import domains.rpi.dto.machines.ExistsMachineUseParameters;
import domains.rpi.serializers.MachineUsesSerializer;
import global.APIException;
import play.libs.F;
import play.mvc.Result;
import policies.MachineUsePolicy;
import policies.actions.Policy;
import queries.machine_uses.MachineUseQuery;

@Policy(MachineUsePolicy.class)
public class MachineUsesController extends RpiBaseController {

    @Tx
    public F.Promise<Result> existsMachineUse(final int machineId) throws APIException {
        MachineUsePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.get();

        ExistsMachineUseParameters dto = new ExistsMachineUseParameters(queryString(), machineId)
            .validate();

        MachineUseQuery query = new MachineUseQuery()
            .filterByMachineId(dto.getMachineId())
            .filterByTimestamp(dto.getTimestamp())
            .filterByAccreditedResult();

        return json(MachineUsesSerializer.itemToJson(query.any()));
    }
}
