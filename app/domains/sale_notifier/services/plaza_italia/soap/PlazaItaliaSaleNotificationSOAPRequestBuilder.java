package domains.sale_notifier.services.plaza_italia.soap;

import domains.sale_notifier.services.plaza_italia.soap.generated.sale.Entrada;
import domains.sale_notifier.services.wrappers.SaleDetailWrapper;
import domains.sale_notifier.services.wrappers.SaleWrapper;
import java.util.List;
import java.util.stream.Collectors;
import utils.ApplicationConfiguration;

public class PlazaItaliaSaleNotificationSOAPRequestBuilder {

    public List<Entrada.WsDeclaVtas> buildSOAPRequest(List<SaleWrapper> salesToNotify) {
        return salesToNotify
            .stream()
            .map(this::buildWsDeclaVtasElement)
            .collect(Collectors.toList());
    }

    private Entrada.WsDeclaVtas buildWsDeclaVtasElement(SaleWrapper saleWrapper) {
        Entrada.WsDeclaVtas wsDeclaVtasElement = new Entrada.WsDeclaVtas();
        Entrada.WsDeclaVtas.General generalElement = buildGeneralElement(saleWrapper);
        wsDeclaVtasElement.setGeneral(generalElement);

        return wsDeclaVtasElement;
    }

    private Entrada.WsDeclaVtas.General buildGeneralElement(SaleWrapper saleWrapper) {
        Entrada.WsDeclaVtas.General generalElement = new Entrada.WsDeclaVtas.General();

        Entrada.WsDeclaVtas.General.Cab cabElement = buildCabElement(saleWrapper);
        generalElement.setCab(cabElement);

        for (SaleDetailWrapper saleDetailWrapper : saleWrapper.getDetail()) {
            Entrada.WsDeclaVtas.General.Det detElement = buildDetElement(saleDetailWrapper);
            generalElement.getDet().add(detElement);
        }

        return generalElement;
    }

    private Entrada.WsDeclaVtas.General.Cab buildCabElement(SaleWrapper saleWrapper) {
        Entrada.WsDeclaVtas.General.Cab cabElement = new Entrada.WsDeclaVtas.General.Cab();

        cabElement.setNumeroRUT(ApplicationConfiguration.getLavomatRUT());
        cabElement.setCodigoShopping("PZI");
        cabElement.setNumeroContrato(49);
        cabElement.setCodigoCanal(saleWrapper.getCodigoCanal());
        cabElement.setMonedaCFE("UYU");
        cabElement.setTipodeCambio(1d);
        cabElement.setCodigoCFE(saleWrapper.getCodigoCFE());
        cabElement.setNumeroCFE(saleWrapper.getNumberoCFE());
        cabElement.setSerieCFE(saleWrapper.getSerieCFE());
        cabElement.setFechaEmisionCFE(saleWrapper.getFechaEmisionCFE());
        cabElement.setTotalMNSIVA(saleWrapper.getTotalMontoSinIva());
        cabElement.setTotalMOCIVA(saleWrapper.getTotalMontoConIva());

        return cabElement;
    }

    private Entrada.WsDeclaVtas.General.Det buildDetElement(SaleDetailWrapper saleDetailWrapper) {
        Entrada.WsDeclaVtas.General.Det detElement = new Entrada.WsDeclaVtas.General.Det();
        detElement.setCodRubro("PZI366");
        detElement.setContadoMNSIVA(0.000);
        detElement.setCreditoMNSIVA(0.000);
        detElement.setDebitoMNSIVA(saleDetailWrapper.getDebitoMontoSinIva());
        detElement.setIncluirenPromo("N");

        return detElement;
    }
}
