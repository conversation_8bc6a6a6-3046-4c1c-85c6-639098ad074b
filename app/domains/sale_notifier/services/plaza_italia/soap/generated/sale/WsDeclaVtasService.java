package domains.sale_notifier.services.plaza_italia.soap.generated.sale;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;

/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 *
 */
@WebServiceClient(
    name = "wsDeclaVtasService",
    targetNamespace = "http://nodum.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas/RESP",
    wsdlLocation = "https://external-soap-services.s3.us-west-2.amazonaws.com/plaza-italia/portal.plazaitaliashopping.com.uy_soap_portal_services_forms_v1.3_wsDeclaVtas_wsdl%26vista%3DRESP.wsdl"
)
public class WsDeclaVtasService extends Service {

    private static final URL WSDECLAVTASSERVICE_WSDL_LOCATION;
    private static final WebServiceException WSDECLAVTASSERVICE_EXCEPTION;
    private static final QName WSDECLAVTASSERVICE_QNAME = new QName(
        "http://nodum.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas/RESP",
        "wsDeclaVtasService"
    );

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url =
                new URL(
                    "https://external-soap-services.s3.us-west-2.amazonaws.com/plaza-italia/portal.plazaitaliashopping.com.uy_soap_portal_services_forms_v1.3_wsDeclaVtas_wsdl%26vista%3DRESP.wsdl"
                );
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        WSDECLAVTASSERVICE_WSDL_LOCATION = url;
        WSDECLAVTASSERVICE_EXCEPTION = e;
    }

    public WsDeclaVtasService() {
        super(__getWsdlLocation(), WSDECLAVTASSERVICE_QNAME);
    }

    public WsDeclaVtasService(WebServiceFeature... features) {
        super(__getWsdlLocation(), WSDECLAVTASSERVICE_QNAME, features);
    }

    public WsDeclaVtasService(URL wsdlLocation) {
        super(wsdlLocation, WSDECLAVTASSERVICE_QNAME);
    }

    public WsDeclaVtasService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, WSDECLAVTASSERVICE_QNAME, features);
    }

    public WsDeclaVtasService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public WsDeclaVtasService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns WsDeclaVtasPortType
     */
    @WebEndpoint(name = "wsDeclaVtasPort")
    public WsDeclaVtasPortType getWsDeclaVtasPort() {
        return super.getPort(
            new QName(
                "http://nodum.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas/RESP",
                "wsDeclaVtasPort"
            ),
            WsDeclaVtasPortType.class
        );
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns WsDeclaVtasPortType
     */
    @WebEndpoint(name = "wsDeclaVtasPort")
    public WsDeclaVtasPortType getWsDeclaVtasPort(WebServiceFeature... features) {
        return super.getPort(
            new QName(
                "http://nodum.com.uy/soap/portal/services/forms/v1.3/wsDeclaVtas/RESP",
                "wsDeclaVtasPort"
            ),
            WsDeclaVtasPortType.class,
            features
        );
    }

    private static URL __getWsdlLocation() {
        if (WSDECLAVTASSERVICE_EXCEPTION != null) {
            throw WSDECLAVTASSERVICE_EXCEPTION;
        }
        return WSDECLAVTASSERVICE_WSDL_LOCATION;
    }
}
