package domains.sale_notifier.services.plaza_italia.soap.generated.sale;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for salida complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="salida">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Resultado" maxOccurs="unbounded">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;all>
 *                   &lt;element name="estado" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                   &lt;element name="mensaje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                   &lt;element name="identificador" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                   &lt;element name="R_wsDeclaVtas">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;all>
 *                             &lt;element name="R_Auditoria">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;sequence>
 *                                       &lt;element name="R_A1">
 *                                         &lt;complexType>
 *                                           &lt;complexContent>
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                               &lt;all>
 *                                                 &lt;element name="R_NumeroTransaccion" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *                                               &lt;/all>
 *                                             &lt;/restriction>
 *                                           &lt;/complexContent>
 *                                         &lt;/complexType>
 *                                       &lt;/element>
 *                                     &lt;/sequence>
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/all>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/all>
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "salida", propOrder = { "resultado" })
public class Salida {

    @XmlElement(name = "Resultado", required = true)
    protected List<Salida.Resultado> resultado;

    /**
     * Gets the value of the resultado property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the resultado property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getResultado().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Salida.Resultado }
     *
     *
     */
    public List<Salida.Resultado> getResultado() {
        if (resultado == null) {
            resultado = new ArrayList<Salida.Resultado>();
        }
        return this.resultado;
    }

    /**
     * <p>Java class for anonymous complex type.
     *
     * <p>The following schema fragment specifies the expected content contained within this class.
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;all>
     *         &lt;element name="estado" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *         &lt;element name="mensaje" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *         &lt;element name="identificador" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *         &lt;element name="R_wsDeclaVtas">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;all>
     *                   &lt;element name="R_Auditoria">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;sequence>
     *                             &lt;element name="R_A1">
     *                               &lt;complexType>
     *                                 &lt;complexContent>
     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                     &lt;all>
     *                                       &lt;element name="R_NumeroTransaccion" type="{http://www.w3.org/2001/XMLSchema}int"/>
     *                                     &lt;/all>
     *                                   &lt;/restriction>
     *                                 &lt;/complexContent>
     *                               &lt;/complexType>
     *                             &lt;/element>
     *                           &lt;/sequence>
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/all>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/all>
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     *
     *
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {})
    public static class Resultado {

        protected int estado;

        @XmlElement(required = true)
        protected String mensaje;

        protected int identificador;

        @XmlElement(name = "R_wsDeclaVtas", required = true)
        protected Salida.Resultado.RWsDeclaVtas rWsDeclaVtas;

        /**
         * Gets the value of the estado property.
         *
         */
        public int getEstado() {
            return estado;
        }

        /**
         * Sets the value of the estado property.
         *
         */
        public void setEstado(int value) {
            this.estado = value;
        }

        /**
         * Gets the value of the mensaje property.
         *
         * @return
         *     possible object is
         *     {@link String }
         *
         */
        public String getMensaje() {
            return mensaje;
        }

        /**
         * Sets the value of the mensaje property.
         *
         * @param value
         *     allowed object is
         *     {@link String }
         *
         */
        public void setMensaje(String value) {
            this.mensaje = value;
        }

        /**
         * Gets the value of the identificador property.
         *
         */
        public int getIdentificador() {
            return identificador;
        }

        /**
         * Sets the value of the identificador property.
         *
         */
        public void setIdentificador(int value) {
            this.identificador = value;
        }

        /**
         * Gets the value of the rWsDeclaVtas property.
         *
         * @return
         *     possible object is
         *     {@link Salida.Resultado.RWsDeclaVtas }
         *
         */
        public Salida.Resultado.RWsDeclaVtas getRWsDeclaVtas() {
            return rWsDeclaVtas;
        }

        /**
         * Sets the value of the rWsDeclaVtas property.
         *
         * @param value
         *     allowed object is
         *     {@link Salida.Resultado.RWsDeclaVtas }
         *
         */
        public void setRWsDeclaVtas(Salida.Resultado.RWsDeclaVtas value) {
            this.rWsDeclaVtas = value;
        }

        /**
         * <p>Java class for anonymous complex type.
         *
         * <p>The following schema fragment specifies the expected content contained within this class.
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;all>
         *         &lt;element name="R_Auditoria">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;sequence>
         *                   &lt;element name="R_A1">
         *                     &lt;complexType>
         *                       &lt;complexContent>
         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                           &lt;all>
         *                             &lt;element name="R_NumeroTransaccion" type="{http://www.w3.org/2001/XMLSchema}int"/>
         *                           &lt;/all>
         *                         &lt;/restriction>
         *                       &lt;/complexContent>
         *                     &lt;/complexType>
         *                   &lt;/element>
         *                 &lt;/sequence>
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/all>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         *
         *
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {})
        public static class RWsDeclaVtas {

            @XmlElement(name = "R_Auditoria", required = true)
            protected Salida.Resultado.RWsDeclaVtas.RAuditoria rAuditoria;

            /**
             * Gets the value of the rAuditoria property.
             *
             * @return
             *     possible object is
             *     {@link Salida.Resultado.RWsDeclaVtas.RAuditoria }
             *
             */
            public Salida.Resultado.RWsDeclaVtas.RAuditoria getRAuditoria() {
                return rAuditoria;
            }

            /**
             * Sets the value of the rAuditoria property.
             *
             * @param value
             *     allowed object is
             *     {@link Salida.Resultado.RWsDeclaVtas.RAuditoria }
             *
             */
            public void setRAuditoria(Salida.Resultado.RWsDeclaVtas.RAuditoria value) {
                this.rAuditoria = value;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;sequence>
             *         &lt;element name="R_A1">
             *           &lt;complexType>
             *             &lt;complexContent>
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                 &lt;all>
             *                   &lt;element name="R_NumeroTransaccion" type="{http://www.w3.org/2001/XMLSchema}int"/>
             *                 &lt;/all>
             *               &lt;/restriction>
             *             &lt;/complexContent>
             *           &lt;/complexType>
             *         &lt;/element>
             *       &lt;/sequence>
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = { "ra1" })
            public static class RAuditoria {

                @XmlElement(name = "R_A1", required = true)
                protected Salida.Resultado.RWsDeclaVtas.RAuditoria.RA1 ra1;

                /**
                 * Gets the value of the ra1 property.
                 *
                 * @return
                 *     possible object is
                 *     {@link Salida.Resultado.RWsDeclaVtas.RAuditoria.RA1 }
                 *
                 */
                public Salida.Resultado.RWsDeclaVtas.RAuditoria.RA1 getRA1() {
                    return ra1;
                }

                /**
                 * Sets the value of the ra1 property.
                 *
                 * @param value
                 *     allowed object is
                 *     {@link Salida.Resultado.RWsDeclaVtas.RAuditoria.RA1 }
                 *
                 */
                public void setRA1(Salida.Resultado.RWsDeclaVtas.RAuditoria.RA1 value) {
                    this.ra1 = value;
                }

                /**
                 * <p>Java class for anonymous complex type.
                 *
                 * <p>The following schema fragment specifies the expected content contained within this class.
                 *
                 * <pre>
                 * &lt;complexType>
                 *   &lt;complexContent>
                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *       &lt;all>
                 *         &lt;element name="R_NumeroTransaccion" type="{http://www.w3.org/2001/XMLSchema}int"/>
                 *       &lt;/all>
                 *     &lt;/restriction>
                 *   &lt;/complexContent>
                 * &lt;/complexType>
                 * </pre>
                 *
                 *
                 */
                @XmlAccessorType(XmlAccessType.FIELD)
                @XmlType(name = "", propOrder = {})
                public static class RA1 {

                    @XmlElement(name = "R_NumeroTransaccion")
                    protected int rNumeroTransaccion;

                    /**
                     * Gets the value of the rNumeroTransaccion property.
                     *
                     */
                    public int getRNumeroTransaccion() {
                        return rNumeroTransaccion;
                    }

                    /**
                     * Sets the value of the rNumeroTransaccion property.
                     *
                     */
                    public void setRNumeroTransaccion(int value) {
                        this.rNumeroTransaccion = value;
                    }
                }
            }
        }
    }
}
