package domains.back_office.services.machines;

import domains.back_office.dto.machines.MachineCreateParameters;
import global.APIException;
import services.BaseService;
import services.MaintenanceService;

public class MachineFactory extends BaseService {

    public void create(MachineCreateParameters dto) throws APIException {
        if (dto.getBuilding() != null) {
            MaintenanceService maintenanceService = new MaintenanceService();
            maintenanceService.assignMaintenanceToNewMachine(dto.getMachine());
        }

        dto.getMachine().save();
    }
}
