package domains.back_office.dto.rates;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.Rate;

public class CreateParameters extends RateParameters {

    public CreateParameters(JsonNode body) throws APIException {
        super(body);
    }

    @Override
    public CreateParameters validate() throws APIException {
        super.validate();

        return this;
    }

    public Rate getRate() {
        return new Rate(this.name, this.type, this.appliesIVA, this.message);
    }
}
