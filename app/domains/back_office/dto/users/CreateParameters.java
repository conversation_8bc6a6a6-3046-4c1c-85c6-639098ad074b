package domains.back_office.dto.users;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import models.User;
import queries.users.UserQuery;

public class CreateParameters extends UserParameters {

    protected UserQuery userQuery;

    public CreateParameters(JsonNode body) throws APIException {
        super(body);
        this.userQuery = new UserQuery();
    }

    public CreateParameters validate() throws APIException {
        super.validate();

        User user = this.userQuery.filterByEmail(this.email).single();
        if (user != null) {
            throw APIException
                .raise(APIException.APIErrors.EMAIL_EXISTS)
                .setDetailMessage("El email ya existe.");
        }

        return this;
    }

    public User getUser() {
        User user = new User(this.email, this.password, this.name, this.lastname, this.role);

        if (this.building != null) {
            user.setBuilding(this.building);
        }

        return user;
    }
}
