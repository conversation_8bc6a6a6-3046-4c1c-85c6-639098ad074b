package domains.back_office.dto.tiny_urls;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import org.apache.commons.lang3.StringUtils;

public class TinyUrlParameters extends dto.JsonBodyActionParameters {

    protected static final String DESTINATION_URL_PARAM = "destinationUrl";
    protected static final String IS_ACTIVE_PARAM = "isActive";
    protected static final String DESCRIPTION_PARAM = "description";

    protected String destinationUrl;
    protected Boolean isActive;
    protected String description;

    public TinyUrlParameters(JsonNode body) throws APIException {
        this.destinationUrl = safeString(DESTINATION_URL_PARAM, body, StringUtils.EMPTY);
        this.description = safeString(DESCRIPTION_PARAM, body, StringUtils.EMPTY);
        this.isActive = safeBoolean(IS_ACTIVE_PARAM, body, false);
    }

    @Override
    public TinyUrlParameters validate() throws APIException {
        return this;
    }

    protected boolean isParamDestinationUrlRequired() {
        return this.isActive && StringUtils.isBlank(this.destinationUrl);
    }
}
