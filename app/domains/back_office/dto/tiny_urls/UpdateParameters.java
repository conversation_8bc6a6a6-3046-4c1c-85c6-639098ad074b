package domains.back_office.dto.tiny_urls;

import com.fasterxml.jackson.databind.JsonNode;
import domains.back_office.exceptions.TinyUrlNotFoundException;
import global.APIException;
import global.exceptions.NonEditableParameterException;
import models.TinyUrl;
import org.apache.commons.lang3.StringUtils;
import play.i18n.Messages;

public class UpdateParameters extends TinyUrlParameters {

    protected TinyUrl tinyUrl;

    public UpdateParameters(TinyUrl tinyUrl, JsonNode body) throws APIException {
        super(body);
        this.tinyUrl = tinyUrl;
    }

    @Override
    public UpdateParameters validate() throws APIException {
        if (this.tinyUrl == null) {
            throw new TinyUrlNotFoundException();
        }

        super.validate();

        // INFO: if it is associated to an entity, the destination url cannot be edited
        if (this.tinyUrl.anyAssociation() && StringUtils.isNotBlank(this.destinationUrl)) {
            throw new NonEditableParameterException(DESTINATION_URL_PARAM);
        }

        // INFO: if it is wanted to activate, the destination url has to have a value
        if (
            this.isParamDestinationUrlRequired() &&
            StringUtils.isBlank(this.tinyUrl.getDestinationUrl())
        ) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(getMissingParametersMessage(DESTINATION_URL_PARAM));
        }

        return this;
    }

    public String getDestinationUrl() {
        return this.destinationUrl;
    }

    public Boolean getIsActive() {
        return this.isActive;
    }

    public String getDescription() {
        return this.description;
    }
}
