package domains.back_office.dto.soapDispensers;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import java.util.Date;
import models.Machine;
import models.SoapDispenser;
import org.apache.commons.lang3.StringUtils;
import utils.TimeZoneUtils;

public class SoapDispenserUpdateParameters extends dto.JsonBodyActionParameters {

    protected static final String MODEL = "model";
    protected static final String DESCRIPTION = "description";
    protected static final String MACHINE_SERIAL = "machineSerial";
    protected static final String REPLENISH_DATE = "replenishDate";
    protected static final String IS_REPLENISH = "isReplenish";

    protected final String model;
    protected final String description;
    protected final String machineSerial;
    protected final Date replenishDate;
    protected final boolean isRecharge;
    private SoapDispenser soapDispenser;
    private Machine machine;

    public SoapDispenserUpdateParameters(JsonNode body, SoapDispenser soapDispenser)
        throws APIException {
        this.model = safeString(MODEL, body, StringUtils.EMPTY);
        this.description = safeString(DESCRIPTION, body, StringUtils.EMPTY);
        this.machineSerial = safeString(MACHINE_SERIAL, body, StringUtils.EMPTY);
        this.replenishDate = safeDate(REPLENISH_DATE, body, null);
        this.isRecharge = safeBoolean(IS_REPLENISH, body, false);
        this.soapDispenser = soapDispenser;
    }

    public SoapDispenserUpdateParameters validate() throws APIException {
        if (this.soapDispenser == null) {
            throw APIException.raise(APIException.APIErrors.SOAP_DISPENSER_NOT_FOUND);
        }

        if (!this.isRecharge) {
            this.machine = Machine.findBySerialNumber(this.machineSerial);
            if (this.machine == null) {
                throw APIException.raise(APIException.APIErrors.MACHINE_NOT_FOUND);
            }
        } else if (this.replenishDate == null) {
            throw APIException
                .raise(APIException.APIErrors.MISSING_PARAMETERS)
                .setDetailMessage(getMissingParametersMessage(REPLENISH_DATE));
        }

        return this;
    }

    public SoapDispenser getSoapDispenser() {
        return this.soapDispenser;
    }

    public Date getReplenishDate() {
        return TimeZoneUtils.getLocalDate(this.replenishDate.getTime(), true, false);
    }

    public String getModel() {
        return this.model;
    }

    public String getDescription() {
        return this.description;
    }

    public Machine getMachine() {
        return this.machine;
    }
}
