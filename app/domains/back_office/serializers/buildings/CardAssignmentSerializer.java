package domains.back_office.serializers.buildings;

import domains.back_office.services.buildings.BuildingUploadFromExcelFileProcessor;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class CardAssignmentSerializer {

    public static JSONObject cardAssignmentToJson(
        BuildingUploadFromExcelFileProcessor.CardAssignment assignment
    ) throws JSONException {
        JSONObject jsonAssignment = new JSONObject();
        jsonAssignment.put("card", assignment.getCard());
        jsonAssignment.put("unit", assignment.getUnit());
        jsonAssignment.put("building", assignment.getBuilding());

        return jsonAssignment;
    }

    public static JSONArray cardAssignmentListToJson(
        List<BuildingUploadFromExcelFileProcessor.CardAssignment> assignments
    ) throws JSONException {
        JSONArray jsonArray = new JSONArray();
        for (BuildingUploadFromExcelFileProcessor.CardAssignment assignment : assignments) {
            jsonArray.put(cardAssignmentToJson(assignment));
        }

        return jsonArray;
    }
}
