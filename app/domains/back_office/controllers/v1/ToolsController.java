package domains.back_office.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.dto.tools.ToolCreateParameters;
import domains.back_office.services.tools.ToolFactory;
import global.APIException;
import play.libs.F;
import play.mvc.Result;
import policies.ToolPolicy;
import policies.actions.Policy;

@Policy(ToolPolicy.class)
public class ToolsController extends BackOfficeBaseController {

    @Tx
    public F.Promise<Result> create() throws APIException {
        ToolPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        ToolCreateParameters dto = new ToolCreateParameters(body()).validate();

        ToolFactory factory = new ToolFactory();
        factory.create(dto);

        return F.Promise.<Result>pure(created());
    }
}
