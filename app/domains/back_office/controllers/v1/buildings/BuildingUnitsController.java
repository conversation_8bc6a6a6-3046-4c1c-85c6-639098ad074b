package domains.back_office.controllers.v1.buildings;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.controllers.v1.BackOfficeBaseController;
import global.APIException;
import models.Building;
import play.libs.F;
import play.mvc.Result;
import policies.actions.Policy;
import policies.buildings.BuildingPolicy;
import queries.buildings.BuildingQuery;
import serializers.UnitSerializer;

@Policy(BuildingPolicy.class)
public class BuildingUnitsController extends BackOfficeBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list(final int buildingId) throws APIException {
        BuildingPolicy allowedPolicy = this.getAllowedPolicy();
        BuildingQuery query = allowedPolicy.get(buildingId);
        Building building = query.single();

        if (building == null) {
            throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
        }

        return json(UnitSerializer.unitListToJson(building.getUnits(), queryLevel(3)));
    }
}
