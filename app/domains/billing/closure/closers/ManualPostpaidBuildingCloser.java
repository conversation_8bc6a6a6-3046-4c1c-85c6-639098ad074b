package domains.billing.closure.closers;

import domains.billing.closure.exceptions.BuildingClosureException;
import domains.billing.closure.steps.AdjustBuildingToWasherMinimumUsesStep;
import domains.billing.closure.steps.GenerateBillItemsFromAdditionalCardsStep;
import domains.billing.closure.steps.GenerateBillItemsFromMachineUsesStep;
import domains.billing.closure.steps.building.GenerateBuildingBillsStep;
import domains.billing.closure.steps.building.GetBuildingAdditionalCardsStep;
import domains.billing.closure.steps.building.GetPostpaidBuildingUsesStep;
import domains.billing.closure.steps.per_unit.AdjustMinimumUsesPerUnitStep;
import java.util.Date;
import models.Building;
import models.ClosureRecord;

public class ManualPostpaidBuildingCloser extends BuildingCloser {

    public ManualPostpaidBuildingCloser(
        Building building,
        Date closureDayAtMidnight,
        Date previousClosureDayAtMidnight,
        ClosureRecord recording
    ) {
        super(building, closureDayAtMidnight, previousClosureDayAtMidnight, recording);
    }

    @Override
    public void close() throws BuildingClosureException {
        new GetPostpaidBuildingUsesStep().run(this);
        new GenerateBillItemsFromMachineUsesStep().run(this);
        new AdjustBuildingToWasherMinimumUsesStep().run(this);
        new AdjustMinimumUsesPerUnitStep().run(this);

        new GetBuildingAdditionalCardsStep().run(this);
        new GenerateBillItemsFromAdditionalCardsStep().run(this);

        new GenerateBuildingBillsStep().run(this);
    }
}
