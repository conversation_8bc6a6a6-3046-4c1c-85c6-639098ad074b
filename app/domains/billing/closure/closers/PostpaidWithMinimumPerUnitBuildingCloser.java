package domains.billing.closure.closers;

import domains.billing.closure.exceptions.BuildingClosureException;
import domains.billing.closure.steps.ConvertBillToAttachmentsStep;
import domains.billing.closure.steps.GenerateBillItemsFromAdditionalCardsStep;
import domains.billing.closure.steps.GenerateBillItemsFromMachineUsesStep;
import domains.billing.closure.steps.GenerateExcelReportStep;
import domains.billing.closure.steps.building.GenerateBuildingBillsStep;
import domains.billing.closure.steps.building.GetBuildingAdditionalCardsStep;
import domains.billing.closure.steps.building.GetPostpaidBuildingUsesStep;
import domains.billing.closure.steps.per_unit.AdjustMinimumUsesPerUnitStep;
import domains.billing.closure.steps.per_unit.CalculateMinimumUsesPerUnitStep;
import java.util.Date;
import models.Building;
import models.ClosureRecord;

public class PostpaidWithMinimumPerUnitBuildingCloser extends BuildingCloser {

    public PostpaidWithMinimumPerUnitBuildingCloser(
        Building building,
        Date closureDayAtMidnight,
        Date previousClosureDayAtMidnight,
        ClosureRecord recording
    ) {
        super(building, closureDayAtMidnight, previousClosureDayAtMidnight, recording);
    }

    @Override
    public void close() throws BuildingClosureException {
        new GetPostpaidBuildingUsesStep().run(this);
        new GenerateBillItemsFromMachineUsesStep().run(this);

        new CalculateMinimumUsesPerUnitStep().run(this);
        new AdjustMinimumUsesPerUnitStep().run(this);

        new GetBuildingAdditionalCardsStep().run(this);
        new GenerateBillItemsFromAdditionalCardsStep().run(this);

        new GenerateExcelReportStep().run(this);
        new GenerateBuildingBillsStep().run(this);
        new ConvertBillToAttachmentsStep().run(this);
    }
}
