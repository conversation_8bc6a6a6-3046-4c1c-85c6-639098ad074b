package domains.billing.closure.steps;

import domains.billing.closure.closers.BuildingCloser;
import java.util.List;
import models.MachineUse;
import models.MachineUseResult;
import queries.machine_uses.MachineUseQuery;

public class GetPrepaidUsesStep implements ClosureStep {

    @Override
    public void run(BuildingCloser closer) {
        List<MachineUse> prepaidUses = new MachineUseQuery()
            .filterForBillingByBuildingId(
                closer.getPreviousClosureDayAtMidnight(),
                closer.getClosureDayAtMidnight(),
                closer.getBuilding().getId(),
                MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE,
                MachineUseResult.WHATSAPP_ACTIVATION_WITH_BALANCE
            )
            .find();

        closer.addUsesToBill(prepaidUses);
    }
}
