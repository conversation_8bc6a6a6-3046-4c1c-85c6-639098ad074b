package domains.billing.closure.steps.per_unit;

import domains.billing.BuildingService;
import domains.billing.closure.closers.BuildingCloser;
import domains.billing.closure.steps.ClosureStep;
import domains.billing.exceptions.BillingException;
import play.Logger;

public class GeneratePerUnitBillsStep implements ClosureStep {

    @Override
    public void run(BuildingCloser closer) {
        try {
            closer
                .getBills()
                .addAll(
                    new BuildingService()
                        .invoiceUnit(
                            closer.getCurrentUnit(),
                            closer.getPreviousClosureDayAtMidnight(),
                            closer.getClosureDayAtMidnight(),
                            closer.getUsesToBill(),
                            closer.getCardToBill(),
                            closer.getBillItems()
                        )
                );
            Logger.debug("Building \"{}\" was billed.", closer.getBuilding().getName());
        } catch (BillingException e) {
            Logger.debug(
                "ERROR - Thrown error during Building \"{}\" is billed (process continues) - error: {}.",
                closer.getBuilding().getName(),
                e.getMessage()
            );
        } catch (Exception e) {
            e.printStackTrace();
            Logger.error("Unexpected exception when creating the bill.", e);
            throw e;
        }
    }
}
