package domains.billing.closure.validations.validators;

import domains.billing.closure.exceptions.ClosureValidationException;
import domains.billing.closure.validations.ClosureValidator;
import java.util.Date;
import models.Building;

public class RechargeableUsesValidator extends ClosureValidator {

    public RechargeableUsesValidator(Building building, Date closureDate) {
        super(building, closureDate);
    }

    @Override
    public void validate() throws ClosureValidationException {
        if (this.building.getPrepaidRechargeableUses() <= 0) {
            throw new ClosureValidationException("PrepaidRechargeableUses must be greater than 0");
        }
    }
}
