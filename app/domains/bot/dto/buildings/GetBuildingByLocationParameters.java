package domains.bot.dto.buildings;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.APIException.APIErrors;

public class GetBuildingByLocationParameters extends dto.JsonBodyActionParameters {

    protected float latitude;
    protected float longitude;

    // source: https://es.wikipedia.org/wiki/Anexo:Puntos_extremos_de_Uruguay
    private static final float MAX_NORTH_LATITUDE = -30.085556f; // -30.085556,-56.951667
    private static final float MAX_SOUTH_LATITUDE = -35.0214391f; // -35.0214391,-54.8905444
    private static final float MAX_EAST_LONGITUDE = -53.182778f; // -32.653889,-53.182778
    private static final float MAX_WEST_LONGITUDE = -58.3065387f; // -33.5058397,-58.3065387

    public GetBuildingByLocationParameters() {}

    public GetBuildingByLocationParameters(JsonNode body) throws APIException {
        latitude = safeFloat("latitude", body, 0f);
        longitude = safeFloat("longitude", body, 0f);
    }

    public GetBuildingByLocationParameters validate() throws APIException {
        if (
            latitude > MAX_NORTH_LATITUDE ||
            latitude < MAX_SOUTH_LATITUDE ||
            longitude > MAX_EAST_LONGITUDE ||
            longitude < MAX_WEST_LONGITUDE
        ) throw APIException.raise(APIErrors.BUILDING_OUT_OF_RANGE);

        return this;
    }

    public float getLatitude() {
        return this.latitude;
    }

    public float getLongitude() {
        return this.longitude;
    }
}
