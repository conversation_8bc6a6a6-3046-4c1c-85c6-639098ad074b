package domains.bot.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.activations.Channel;
import domains.activations.MachineActivationService;
import domains.bot.dto.machines.GetActivationParameters;
import domains.bot.dto.machines.GetConfirmActivationParameters;
import domains.bot.services.support.ContactSupportService;
import global.APIException;
import java.io.IOException;
import org.json.JSONException;
import play.libs.F;
import play.mvc.Result;
import policies.MachineActivationPolicy;
import policies.actions.Policy;

@Policy(MachineActivationPolicy.class)
public class MachineActivationController extends BotBaseController {

    @Tx
    public F.Promise<Result> activation(Integer machineId, String cardUid)
        throws APIException, IOException, JSONException {
        MachineActivationPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        GetActivationParameters params = new GetActivationParameters(machineId, cardUid).validate();

        MachineActivationService
            .instance()
            .usingCard(params.getCard())
            .atBuilding(params.getBuilding())
            .forMachine(params.getMachine())
            .sendingMqttMessage()
            .viaWhatsApp()
            .activate();

        return F.Promise.<Result>pure(noContent());
    }

    @Tx(readOnly = true)
    public F.Promise<Result> confirmActivation(Integer machineId, String cardUid)
        throws APIException, Exception {
        MachineActivationPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.get();

        GetConfirmActivationParameters params = new GetConfirmActivationParameters(
            body(),
            machineId,
            cardUid
        )
            .validate();

        ContactSupportService service = new ContactSupportService();
        service.confirmActivation(
            params.getUid(),
            params.getBuilding().getId(),
            params.getMachine().getId(),
            params.getResult()
        );

        return F.Promise.<Result>pure(noContent());
    }
}
