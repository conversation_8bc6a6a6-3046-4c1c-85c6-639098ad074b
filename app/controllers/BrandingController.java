package controllers;

import com.play4jpa.jpa.db.Tx;
import dto.branding_item.*;
import global.APIException;
import global.APIException.APIErrors;
import global.ErrorMessage;
import java.util.List;
import models.BrandingItem;
import models.BrandingItemRequest;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import policies.ApiClient;
import policies.actions.Policy;
import policies.branding_items.BrandingItemPolicy;
import policies.branding_items.BrandingItemRequestPolicy;
import queries.branding_items.BrandingItemQuery;
import queries.branding_items.BrandingItemRequestQuery;
import security.v1.Secured;
import serializers.BrandingItemRequestsSerializer;
import serializers.BrandingItemSerializer;
import services.BrandingItemService;

@ErrorMessage
@global.LoggingMessage
public class BrandingController extends AbstractController {

    @Tx(readOnly = true)
    @Policy(BrandingItemPolicy.class)
    public F.Promise<Result> getItems() throws APIException {
        BrandingItemPolicy allowedPolicy = this.getAllowedPolicy();
        BrandingItemQuery query = allowedPolicy.list();

        return json(
            BrandingItemSerializer.itemListToJson(
                query.find(),
                queryLevel(0, allowedPolicy.level())
            )
        );
    }

    @Tx(readOnly = true)
    @Security.Authenticated(Secured.class)
    @Policy(BrandingItemRequestPolicy.class)
    public F.Promise<Result> getRequestedItems() throws APIException {
        BrandingItemRequestPolicy allowedPolicy = this.getAllowedPolicy();
        BrandingItemRequestQuery query = allowedPolicy.list();

        return json(
            BrandingItemRequestsSerializer.itemListToJson(
                query.find(),
                queryLevel(1, allowedPolicy.level(ApiClient.BACKOFFICE))
            )
        );
    }

    @Tx
    @Security.Authenticated(Secured.class)
    @Policy(BrandingItemRequestPolicy.class)
    public F.Promise<Result> deliverRequestedItem(int brandingItemRequestId) throws APIException {
        BrandingItemRequestPolicy allowedPolicy = this.getAllowedPolicy();
        BrandingItemRequestQuery query = allowedPolicy.update(brandingItemRequestId);
        BrandingItemRequest request = query.single();

        if (request == null) {
            throw APIException.raise(APIErrors.BRANDING_ITEM_REQUEST_NOT_FOUND);
        }

        request.deliver();

        return F.Promise.<Result>pure(ok());
    }

    @Tx
    @Security.Authenticated(Secured.class)
    @Policy(BrandingItemPolicy.class)
    public F.Promise<Result> createItem() throws APIException {
        BrandingItemPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        CreateBrandingItemParameters params = new CreateBrandingItemParameters(body()).validate();

        BrandingItemService service = new BrandingItemService();
        service.createItem(params);

        return F.Promise.<Result>pure(ok());
    }

    @Tx
    @Security.Authenticated(Secured.class)
    @Policy(BrandingItemPolicy.class)
    public F.Promise<Result> updateItem(int id) throws APIException {
        BrandingItemPolicy allowedPolicy = this.getAllowedPolicy();
        BrandingItemQuery query = allowedPolicy.update(id);

        UpdateBrandingItemParameters params = new UpdateBrandingItemParameters(
            query.single(),
            body()
        )
            .validate();

        BrandingItemService service = new BrandingItemService();
        service.updateItem(params);

        return F.Promise.<Result>pure(ok());
    }

    @Tx
    @Security.Authenticated(Secured.class)
    @Policy(BrandingItemPolicy.class)
    public F.Promise<Result> deleteItem(int id) throws APIException {
        BrandingItemPolicy allowedPolicy = this.getAllowedPolicy();
        BrandingItemQuery query = allowedPolicy.delete(id);

        DeleteBrandingItemParameters params = new DeleteBrandingItemParameters(query.single())
            .validate();

        BrandingItemService service = new BrandingItemService();
        service.deleteItem(params);

        return F.Promise.<Result>pure(ok());
    }
}
