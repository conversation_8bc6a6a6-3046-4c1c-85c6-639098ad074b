package controllers.tasks.v1;

import com.play4jpa.jpa.db.Tx;
import controllers.AbstractController;
import domains.exchange_rate.services.ExchangeRateService;
import global.APIException;
import global.ErrorMessage;
import global.PermissionValidator;
import java.util.List;
import javax.inject.Inject;
import models.BuildingAverageUses;
import models.PreventiveMaintenanceBuildingEntry;
import models.Role;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import queries.machine_uses.SurgePricingQuery;
import security.v1.Secured;
import serializers.MaintenanceSerializer;
import services.MaintenanceService;
import services.building.ColivingBuildingService;
import services.cache.CacheService;
import services.cache.redis.RedisService;
import services.user.ColivingUsersService;

@ErrorMessage
@global.LoggingMessage
@Security.Authenticated(Secured.class)
public class TasksController extends AbstractController {

    @Inject
    ColivingBuildingService colivingBuildingService;

    @Inject
    ColivingUsersService colivingUsersService;

    @Inject
    ExchangeRateService exchangeRateService;

    @Inject
    MaintenanceService maintenanceService;

    CacheService cacheService;

    @Inject
    public TasksController(RedisService redisService) {
        this.cacheService = redisService;
    }

    @Tx
    public Promise<Result> rechargeColivingPrepaidUses() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                this.colivingBuildingService.rechargeColivingBuildingsUses();
                return Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx
    public Promise<Result> invalidateOldTokens() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                this.colivingUsersService.invalidateOldTokens();
                return Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx
    public Promise<Result> updateUIExchangeRate() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                this.exchangeRateService.updateUIExchangeRate();
                return Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx
    public Promise<Result> updateUSDExchangeRate() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                this.exchangeRateService.updateUSDExchangeRate();
                return Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx(readOnly = true)
    public Promise<Result> hydratePreventiveMaintenanceCache() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                cacheService.cache(
                    "preventive",
                    () -> {
                        MaintenanceService service = new MaintenanceService();
                        List<PreventiveMaintenanceBuildingEntry> entries = service.mapBuildingEntriesWithMaintenanceParameters();
                        List<BuildingAverageUses> buildingAverageUses = service.listBuildingsAverageUses();
                        try {
                            return MaintenanceSerializer
                                .preventiveMaintenanceEntryWithBuildingAveragesListToJson(
                                    entries,
                                    buildingAverageUses
                                )
                                .toString();
                        } catch (APIException e) {
                            throw new RuntimeException(e);
                        }
                    }
                );

                return Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx(readOnly = true)
    public Promise<Result> c30() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                new SurgePricingQuery().p2c30();
                return Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    @Tx(readOnly = true)
    public Promise<Result> c1() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                new SurgePricingQuery().p2c1();
                return Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }
}
