package controllers;

import com.play4jpa.jpa.db.Tx;
import dto.audit.GetAuditParameters;
import global.APIException;
import global.ErrorMessage;
import global.PermissionValidator;
import java.util.List;
import java.util.Map;
import models.Audit;
import models.Role;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import serializers.AuditSerializer;

@ErrorMessage
@global.LoggingMessage
@Security.Authenticated(Secured.class)
public class AuditController extends AbstractController {

    @Tx(readOnly = true)
    public Promise<Result> getAudits() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PermissionValidator.PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    GetAuditParameters parameters = new GetAuditParameters(queryString())
                        .validate();

                    List<Audit> audits = Audit.find(
                        parameters.getFrom(),
                        parameters.getTo(),
                        parameters.getPage(),
                        parameters.getPerPage(),
                        parameters.getUid(),
                        parameters.getOrder(),
                        parameters.getOrderDirection()
                    );

                    return json(AuditSerializer.auditListToJson(audits, queryLevel(0)).toString());
                }
            },
            Role.MASTER,
            Role.ASSISTANT
        );
    }
}
