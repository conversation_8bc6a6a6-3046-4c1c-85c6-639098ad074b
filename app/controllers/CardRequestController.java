package controllers;

import com.play4jpa.jpa.db.Tx;
import dto.card_request.GetRejectActivationParameters;
import global.APIException;
import global.ErrorMessage;
import java.util.Map;
import play.libs.F.Promise;
import play.mvc.Result;
import play.twirl.api.Html;
import services.RequestNewCardService;
import views.html.generalMessage;

@ErrorMessage
@global.LoggingMessage
@Tx
public class CardRequestController extends AbstractController {

    public Promise<Result> confirmNewCard(String token) throws APIException {
        try {
            RequestNewCardService service = new RequestNewCardService();
            service.confirmNewCard(token);

            Html view = generalMessage.render("La solicitud ha sido aceptada con éxito!", "");
            return Promise.<Result>pure(ok(view));
        } catch (Exception e) {
            e.printStackTrace();
            String message = "";
            if (
                e instanceof APIException &&
                ((APIException) e).getArg("INVALID_CARD_REQUEST_TOKEN") == "true"
            ) {
                message = "La solicitud ya fue confirmada previamente.";
            } else {
                message = "Ha ocurrido un error en la solicitud de la nueva tarjeta.";
            }
            Html view = generalMessage.render(message, "");
            return Promise.<Result>pure(badRequest(view));
        }
    }

    public Promise<Result> rejectNewCard(String token) throws APIException {
        try {
            RequestNewCardService service = new RequestNewCardService();
            service.rejectNewCard(token);

            Html view = views.html.generalMessage.render(
                "La solicitud ha sido rechazada con éxito!",
                ""
            );
            return Promise.<Result>pure(ok(view));
        } catch (Exception e) {
            e.printStackTrace();
            String message = "";
            if (
                e instanceof APIException &&
                ((APIException) e).getArg("INVALID_CARD_REQUEST_TOKEN") == "true"
            ) {
                message = "La solicitud ya fue rechazada previamente.";
            } else {
                message = "Ha ocurrido un error mientras rechazabas una nueva tarjeta.";
            }
            Html view = generalMessage.render(message, "");
            return Promise.<Result>pure(badRequest(view));
        }
    }

    public Promise<Result> activate(String token) throws APIException {
        try {
            RequestNewCardService service = new RequestNewCardService();
            service.acceptCard(token);

            Html view = views.html.generalMessage.render(
                "La solicitud ha sido aceptada con éxito!",
                ""
            );
            return Promise.<Result>pure(ok(view));
        } catch (Exception e) {
            e.printStackTrace();
            String message = "";
            if (
                e instanceof APIException &&
                ((APIException) e).getArg("INVALID_CARD_REQUEST_TOKEN") == "true"
            ) {
                message = "La solicitud ya fue aceptada previamente.";
            } else {
                message = "Ha ocurrido un error en la solicitud de la aceptacion de la tarjeta.";
            }
            Html view = generalMessage.render(message, "");
            return Promise.<Result>pure(badRequest(view));
        }
    }

    public Promise<Result> rejectActivation(String token) throws APIException {
        try {
            Html view = views.html.keepBlockedCard.render(token);
            return Promise.<Result>pure(ok(view));
        } catch (Exception e) {
            e.printStackTrace();
            String message =
                "Ha ocurrido un error en la solicitud de la rechazo de aceptación de la tarjeta.";
            Html view = generalMessage.render(message, "");
            return Promise.<Result>pure(badRequest(view));
        }
    }

    public Promise<Result> rejectActivationSubmit() throws APIException {
        try {
            Map<String, String[]> body = request().body().asFormUrlEncoded();

            GetRejectActivationParameters params = new GetRejectActivationParameters(body)
                .validate();
            RequestNewCardService service = new RequestNewCardService();
            service.rejectActivationSubmit(params.getToken(), params.getReason());

            Html view = views.html.generalMessage.render(
                "La solicitud ha sido aceptada con éxito!",
                ""
            );
            return Promise.<Result>pure(ok(view));
        } catch (Exception e) {
            e.printStackTrace();
            String message =
                "Ha ocurrido un error en la solicitud de la rechazo de aceptación de la tarjeta.";
            Html view = generalMessage.render(message, "");
            return Promise.<Result>pure(badRequest(view));
        }
    }
}
