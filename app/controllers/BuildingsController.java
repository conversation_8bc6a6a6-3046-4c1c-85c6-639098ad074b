package controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import domains.back_office.services.soap_dispensers.SoapDispenserService;
import domains.payment_gateways.services.mercado_pago.MercadoPagoTransactionFlow;
import dto.building.GetBuildingParameters;
import dto.machine_use.MachineUseParameters;
import global.APIException;
import global.APIException.APIErrors;
import global.BackOfficeCop;
import global.ErrorMessage;
import global.PermissionValidator;
import global.PermissionValidator.PromiseCallback;
import global.exceptions.*;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.PersistenceException;
import models.*;
import models.Audit.ResultCode;
import models.Card.ContractType;
import models.Currency;
import models.Machine.MachineType;
import models.Machine.StatusActivation;
import models.Maintenance.MaintenanceType;
import models.Part.PartState;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONException;
import org.json.JSONObject;
import play.libs.F.Promise;
import play.mvc.Http.MultipartFormData;
import play.mvc.Http.MultipartFormData.FilePart;
import play.mvc.Result;
import play.mvc.Security;
import policies.ApiClient;
import policies.actions.Policy;
import policies.buildings.BuildingPolicy;
import queries.buildings.BuildingQuery;
import queries.cards.CardQuery;
import queries.machines.MachineQuery;
import queries.users.UserQuery;
import security.v1.Secured;
import serializers.BuildingSerializer;
import serializers.HashSerializer;
import serializers.MachineSerializer;
import services.building.BuildingRpiService;
import services.card.CardFactoryService;
import services.machine.MachineHistoryService;
import services.user.UsesAccreditationService;

@ErrorMessage
@global.LoggingMessage
@Security.Authenticated(Secured.class)
@Policy(BuildingPolicy.class)
public class BuildingsController extends AbstractController {

    @Tx
    @BackOfficeCop
    public Promise<Result> cardAction(
        final int buildingId,
        final int unitId,
        final String cardId,
        final String action
    ) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = new BuildingQuery().get(buildingId);
                    if (building == null) {
                        throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
                    }

                    Card card = Card.findByUID(cardId);
                    if (card == null) {
                        throw APIException.raise(APIErrors.CARD_NOT_FOUND);
                    }

                    Optional<Unit> unit = building
                        .getUnits()
                        .stream()
                        .filter(u -> u.getId() == unitId)
                        .findFirst();
                    if (!unit.isPresent()) {
                        throw APIException.raise(APIErrors.UNIT_NOT_FOUND);
                    }

                    switch (action) {
                        case "activate":
                            card.enable();
                            break;
                        case "deactivate":
                            card.disable();
                            break;
                        case "lost":
                            card.lost();
                            break;
                        case "damaged":
                            card.damaged();
                            break;
                        case "preBlocked":
                            card.preBlock();
                            break;
                        default:
                            break;
                    }
                    card.update();

                    return Promise.<Result>pure(created());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.ASSISTANT
        );
    }

    @Tx
    @BackOfficeCop
    public Promise<Result> deleteBuilding(final int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = new BuildingQuery().get(buildingId);

                    if (building == null) throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);

                    building.delete();

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER
        );
    }

    @Tx(readOnly = true)
    public Result listBuildings() throws APIException {
        BuildingPolicy allowedPolicy = this.getAllowedPolicy();
        BuildingQuery query = allowedPolicy.list();

        return ok(
            BuildingSerializer
                .buildingListToJson(
                    query.find(),
                    queryLevel(1, allowedPolicy.level(ApiClient.BACKOFFICE))
                )
                .toString()
        )
            .as("application/json");
    }

    @Tx(readOnly = true)
    public Promise<Result> getBuilding(final int buildingId) throws APIException {
        Building building = new BuildingQuery().get(buildingId);

        return json(BuildingSerializer.buildingToJson(building, 3));
    }

    @Tx
    public Promise<Result> createUnit(final int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();

                    String number = safeString("number", body);
                    String tower = safeString("tower", body, "1");
                    String ownerEmail = safeString("contact", body);

                    Building building = new BuildingQuery().get(buildingId);
                    if (building == null) throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);

                    Unit unit = new Unit(number, tower, ownerEmail);
                    unit.save();

                    building.getUnits().add(unit);
                    building.update();

                    return Promise.<Result>pure(created());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.ASSISTANT
        );
    }

    @Tx
    public Promise<Result> updateUnit(final int buildingId, final int unitId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();

                    String number = safeString("number", body);
                    String tower = safeString("tower", body);
                    String ownerEmail = safeString("contact", body);

                    Building building = new BuildingQuery().get(buildingId);
                    if (building == null) throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);

                    Unit u = null;

                    for (Unit unit : building.getUnits()) {
                        if (unit.getId() == unitId) {
                            u = unit;
                            break;
                        }
                    }

                    if (u == null) throw APIException.raise(APIErrors.UNIT_NOT_FOUND);

                    u.setNumber(number);

                    User owner = User.findByEmailAddress(ownerEmail);

                    if (owner != null) u.setOwner(owner);

                    if (tower != null) u.setTower(tower);

                    u.setContact(ownerEmail);

                    u.update();

                    return Promise.<Result>pure(created());
                }
            },
            Role.MASTER,
            Role.ADMIN
        );
    }

    @Tx
    public Promise<Result> deleteUnit(final int buildingId, final int unitId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = new BuildingQuery().get(buildingId);
                    if (building == null) throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);

                    Unit u = null;

                    for (Unit unit : building.getUnits()) {
                        if (unit.getId() == unitId) {
                            u = unit;
                            break;
                        }
                    }

                    if (u == null) throw APIException.raise(APIErrors.UNIT_NOT_FOUND);

                    building.getUnits().remove(u);
                    u.delete();

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN
        );
    }

    @Tx
    @BackOfficeCop
    public Promise<Result> assignMachine(final int buildingId, final int machineId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = new BuildingQuery().get(buildingId);
                    if (building == null) {
                        throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
                    }

                    Machine machine = new MachineQuery().get(machineId);
                    if (machine == null) {
                        throw APIException.raise(APIErrors.MACHINE_NOT_FOUND);
                    }

                    if (
                        machine.getMachineType() == MachineType.DRYER &&
                        building.getDryersCount() == 0
                    ) {
                        Maintenance mp1200 = new Maintenance(
                            building,
                            MaintenanceType.MP1200,
                            "Primera Asignación Secadora",
                            new Date()
                        );
                        mp1200.save();
                    } else if (
                        machine.getMachineType() == MachineType.WASHER &&
                        building.getWashersCount() == 0
                    ) {
                        Maintenance mp500 = new Maintenance(
                            building,
                            MaintenanceType.MP500,
                            "Primera Asignación Lavadora",
                            new Date()
                        );
                        mp500.save();

                        Maintenance mp100 = new Maintenance(
                            building,
                            MaintenanceType.MP100,
                            "Primera Asignación Lavadora",
                            new Date()
                        );
                        mp100.save();
                    }

                    machine.setBuilding(building);
                    machine.update();

                    if (machine.getQr() != null) {
                        domains.back_office.services.tiny_urls.AssignationService service = new domains.back_office.services.tiny_urls.AssignationService(
                            machine.getQr()
                        );
                        service.refresh(machine);
                    }

                    new MachineHistoryService().registerMachineRecord(machine);

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.ASSISTANT
        );
    }

    @Tx
    @BackOfficeCop
    public Promise<Result> unassignMachine(final int buildingId, final int machineId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = Building.findById(buildingId);
                    if (building == null) {
                        throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
                    }

                    Machine machine = Machine.findById(machineId);

                    if (machine == null) {
                        throw APIException.raise(APIErrors.MACHINE_NOT_FOUND);
                    }

                    machine.setBuilding(null);
                    machine.update();

                    if (machine.getQr() != null) {
                        domains.back_office.services.tiny_urls.AssignationService service = new domains.back_office.services.tiny_urls.AssignationService(
                            machine.getQr()
                        );
                        service.refresh(machine);
                    }

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.ASSISTANT
        );
    }

    @Tx
    @BackOfficeCop
    public Promise<Result> assignCardToUnit(
        final int buildingId,
        final int unitId,
        final String cardUuid
    ) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = new BuildingQuery().get(buildingId);
                    if (building == null) {
                        throw new BuildingNotFoundException();
                    }

                    Card card = Card.findByUID(cardUuid);
                    if (card == null) {
                        throw new CardNotFoundException();
                    }

                    if (ContractType.PREPAID == building.getContractType() && !card.isPrepaid()) {
                        throw new IncompatibleContractTypeException();
                    }

                    if (card.getUnit() != null) {
                        throw APIException
                            .raise(APIErrors.NOT_ASSIGNABLE)
                            .setDetailMessage("Esta tarjeta ya se encuentra asignada");
                    }

                    Unit unit = building
                        .getUnits()
                        .stream()
                        .filter(buildingUnit -> buildingUnit.getId() == unitId)
                        .findFirst()
                        .orElseThrow(UnitNotFoundException::new);

                    boolean billable = Boolean.parseBoolean(request().getQueryString("billable"));
                    Rate rate = building.getRate();
                    if (billable && rate == null) {
                        throw new RateNotFoundException();
                    }

                    unit.getAssignedCards().add(card);
                    card.setUnit(unit);

                    unit.update();

                    CardEvent cardEvent = null;
                    if (billable) {
                        if (card.getContractType() == Card.ContractType.PREPAID) {
                            card.setBalance(rate.getPriceCardReplacement() * -1);
                            cardEvent =
                                new CardEvent(CardEventType.CARD_ASSIGNED_NON_BILLABLE, card);
                        } else {
                            cardEvent = new CardEvent(CardEventType.CARD_ASSIGNED_BILLABLE, card);
                        }
                    } else {
                        cardEvent = new CardEvent(CardEventType.CARD_ASSIGNED_NON_BILLABLE, card);
                    }

                    card.update();
                    cardEvent.save();

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.ASSISTANT
        );
    }

    @Tx
    @BackOfficeCop
    public Promise<Result> unassignCardFromUnit(
        final int buildingId,
        final int unitId,
        final String cardId
    ) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = Building.findById(buildingId);
                    if (building == null) {
                        throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
                    }

                    Card card = Card.findByUID(cardId);

                    if (card == null) {
                        throw APIException.raise(APIErrors.CARD_NOT_FOUND);
                    }

                    Unit unit = null;

                    for (int i = 0; i < building.getUnits().size(); i++) {
                        if (building.getUnits().get(i).getId() == unitId) {
                            unit = building.getUnits().get(i);
                            break;
                        }
                    }

                    if (unit == null) {
                        throw APIException.raise(APIErrors.UNIT_NOT_FOUND);
                    }

                    if (unit.getAssignedCards().contains(card)) {
                        unit.getAssignedCards().remove(card);
                        card.setUnit(null);

                        unit.update();
                        card.unassign();
                    } else throw APIException
                        .raise(APIErrors.WRONG_VALUE)
                        .setDetailMessage("Esta tarjeta no pertenece a la unidad");

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.ASSISTANT
        );
    }

    @Tx(readOnly = true)
    public Promise<Result> getRpiCount(int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    BuildingRpiService service = new BuildingRpiService(buildingId);
                    int count = service.getRpiCount();

                    Map<String, Object> hash = new HashMap<>();
                    hash.put("count", count);

                    return json(HashSerializer.hashToJson(hash).toString());
                }
            },
            Role.RPI
        );
    }

    @Tx
    public Promise<Result> pushUses(final int buildingId) throws APIException, JSONException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode uses = body().get("uses");
                    int failed = 0, success = 0;

                    for (JsonNode jsonUse : uses) {
                        MachineUse machineUse = useFromJson(jsonUse);

                        if (machineUse.isAccredited()) {
                            Machine machine = machineUse.getMachine();
                            machine.incrementUsesCount();
                            machine.update();
                        }

                        if (machineUse != null) {
                            try {
                                machineUse.save();
                                success++;

                                updateBalanceOfCards(machineUse);
                            } catch (PersistenceException e) {
                                if (e.getCause() instanceof ConstraintViolationException) {
                                    success++;
                                } else {
                                    failed++;
                                }
                            } catch (Throwable e) {
                                e.printStackTrace();
                                failed++;
                            }

                            forceTransactionFlush(true);
                        }
                    }

                    JSONObject response = new JSONObject();
                    try {
                        response.put("processed_ok", success);
                        response.put("processed_error", failed);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                    return json(response.toString());
                }
            },
            Role.RPI
        );
    }

    @Tx
    private void updateBalanceOfCards(MachineUse use) {
        try {
            this.createAuditWorkflowForUse(use);

            double discount = use.getCard() != null ? use.getCard().getDiscount() : 0;

            // Considernado machine rate por posible dosificación de jabón
            double price = use.getMachine().getMachineRate() != null
                ? use.getMachine().getMachineRate().getPriceCustomer()
                : use.getCard().getUnit().getBuilding().getRate().getPriceCustomer();

            // Liberamos el estado de activación
            use.getMachine().setStatusActivation(StatusActivation.READY);
            use.getMachine().update();

            double finalPrice = price - (price * discount);

            if (finalPrice == 0.0) {
                throw new UnableToDebitCardException(use.getUid());
            }

            Card card = use.getCard();
            MachineUseResult result = MachineUseResult.getEnum(use.getResult());

            // If the machine has soap dispenser, then add uses count
            SoapDispenserService service = new SoapDispenserService();
            service.increaseUseCount(use.getMachine(), result, card);

            switch (result) {
                case PREPAID_ACTIVATION_WITH_BALANCE:
                case WHATSAPP_ACTIVATION_WITH_BALANCE:
                    Audit audit = new Audit(
                        Audit.TransferType.DEBIT,
                        finalPrice,
                        Currency.UYU,
                        card.getUuid(),
                        card.getUuid(),
                        "USO",
                        0,
                        ResultCode.OK,
                        card.getBalance(),
                        card.getBalance() - finalPrice
                    );
                    audit.save();
                    double newBalance = card.getBalance() - finalPrice;
                    card.setBalance(Math.round(newBalance * 100.0) / 100.0);
                    card.update();
                    break;
                case QR:
                    Thread thread = new Thread(runnable(use.getTransactionId()));
                    thread.start();
                    break;
                case POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITH_BALANCE:
                    if (card.getState() != PartState.PRE_BLOCKED) {
                        break;
                    }

                    card.decreasePreBlockedUses();
                    if (card.getPreBlockedUses() <= 0) {
                        card.disable();
                    }
                    if (card.getPreBlockedUses() < 0) {
                        play.Logger.error(
                            "Card: " + card.getUuid() + " has less than 0 pre blocked uses."
                        );
                    }

                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            play.Logger.error(
                "Don't substract customer price to uid '" +
                use.getUid() +
                "' and others cards for the unit."
            );
            e.printStackTrace();
        }
    }

    private Runnable runnable(int tx_use) {
        play.Logger.debug("START");
        AuditWorkflow auditWorkflow = AuditWorkflow.findById(tx_use);
        MercadoPagoTransactionFlow mp = new MercadoPagoTransactionFlow();
        try {
            mp.confirmTransaction(auditWorkflow);
        } catch (Exception e) {
            e.printStackTrace();
        }
        play.Logger.debug("END");
        return null;
    }

    @Tx
    public Promise<Result> uploadUses(final int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = Building.findById(buildingId);
                    if (building == null) {
                        throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
                    }

                    MultipartFormData body = request().body().asMultipartFormData();
                    FilePart fd = body.getFile("file");

                    int failed = 0, success = 0;

                    if (fd != null) {
                        File file = fd.getFile();

                        try {
                            Scanner scanner = new Scanner(file);
                            scanner.useDelimiter("\n");
                            while (scanner.hasNext()) {
                                String line = scanner.next();
                                MachineUse use = useFromLine(line);

                                if (use != null) {
                                    try {
                                        use.save();
                                        success++;
                                        forceTransactionFlush(true);
                                    } catch (Throwable e) {
                                        failed++;
                                        forceTransactionFlush(true);
                                    }
                                    scanner.close();
                                }
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                            throw APIException.raise(APIErrors.WRONG_FORMAT);
                        }

                        JSONObject response = new JSONObject();
                        try {
                            response.put("processed_ok", success);
                            response.put("processed_error", failed);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }

                        return json(response.toString());
                    } else {
                        throw APIException.raise(APIErrors.WRONG_FORMAT);
                    }
                }
            },
            Role.MASTER
        );
    }

    @Tx
    private static MachineUse useFromLine(String line) {
        MachineUse use = new MachineUse();

        String[] tokens = line.replaceAll("\\r", "").split("\\,");

        if (line.length() == 0) {
            return null;
        }

        String machineToken = tokens[0];
        String timeToken = tokens[1];
        String cardToken = tokens[2];

        String machineSerial = machineToken.trim();
        String uid = cardToken.replace(" UID: ", "");

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        try {
            Date timestamp = sdf.parse(timeToken);
            use.setTimestamp(timestamp);

            Machine machine = Machine.findBySerialNumber(machineSerial);
            Card card = Card.findByUID(uid);

            use.setHeadline(
                (machine.getMachineType() == MachineType.WASHER ? "Lavado" : "Secado") +
                ", Máquina: " +
                machineSerial +
                ", Tarjeta: " +
                uid
            );

            if (machine != null) {
                if (use.isAccredited()) {
                    machine.setCurrentUses(machine.getCurrentUses() + 1);
                    machine.update();
                }

                use.setMachine(machine);
                use.setBuilding(machine.getBuilding());
            }

            if (card != null) {
                use.setCard(card);
            }

            use.setUid(uid);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return use;
    }

    @Tx
    private static MachineUse useFromJson(JsonNode json) throws APIException {
        if (json == null) {
            return null;
        }

        MachineUseParameters parameters = new MachineUseParameters(json).validate();

        return parameters.getMachineUse();
    }

    @Tx
    public Promise<Result> cardAction(String uid, String action) throws APIException {
        Card card = (Card) Card.findByUID(uid);

        if (card == null) throw APIException.raise(APIErrors.CARD_NOT_FOUND);

        switch (action) {
            case "activate":
                card.enable();
                break;
            case "deactivate":
                card.disable();
                break;
            case "suspend":
                card.suspend();
                break;
        }
        return Promise.<Result>pure(ok());
    }

    // Obtener listado de edificios de tipo laundromat
    @Tx
    public Promise<Result> getLaundromats(int userId) throws APIException {
        JSONObject response = new JSONObject();

        Card card = new CardQuery().filterByCardHolderPrepaidId(userId).single();

        if (card == null) {
            User user = new UserQuery().get(userId);
            CardFactoryService cardFactory = new CardFactoryService();
            card = cardFactory.createRegularUserVirtualCard(user);
        }

        List<Building> laundromats = new BuildingQuery().filterLaundromats().find();

        try {
            ArrayList<JSONObject> list = new ArrayList<>();

            for (Building building : laundromats) {
                JSONObject buildingJson = new JSONObject();

                buildingJson.put("id", building.getId());
                buildingJson.put("city", building.getCity());
                buildingJson.put("country", building.getCountry());
                buildingJson.put("departament", building.getDepartment());
                buildingJson.put("address", building.getAddress());
                buildingJson.put("enable", building.isRemoteActivationEnabled());
                buildingJson.put("name", building.getName());
                buildingJson.put("type", building.getBuildingType());
                buildingJson.put(
                    "virtual_uuid",
                    card.getUuid() != null ? card.getUuid() : "NO_NUMBER"
                );
                list.add(buildingJson);
                response.put("Laundromats", list);
            }

            // se retorna lista vacia
            if (list.isEmpty()) {
                response.put("Laundromats", "");
            }
        } catch (JSONException e) {}

        return json(response.toString());
    }

    @Tx
    public Promise<Result> rechargePrepaidUses(int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    UsesAccreditationService service = new UsesAccreditationService();

                    try {
                        Building building = Building.findById(buildingId);
                        service.rechargePrepaidUsesToBuilding(building);
                    } catch (Exception e) {
                        play.Logger.error(e.toString());
                    }

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN
        );
    }

    @Tx
    public Promise<Result> getMachinesWithDifferentsRatesByBuildingId(int buildingId)
        throws APIException {
        GetBuildingParameters parameters = new GetBuildingParameters(buildingId).validate();

        List<Machine> machineList = parameters
            .getBuilding()
            .getMachines()
            .stream()
            .filter(Machine::hasSpecialRate)
            .collect(Collectors.toList());

        return json(MachineSerializer.machineListToJson(machineList, 0).toString());
    }

    @Tx
    private void createAuditWorkflowForUse(MachineUse use) {
        try {
            if (use.getTransactionId() > 0) {
                AuditWorkflow auditWorkflow = new AuditWorkflow(
                    "Generating Machine Use ",
                    AuditWorkflow.Status.COMPLETED,
                    use.getTransactionId()
                );
                auditWorkflow.save();
                // Agendamos envío de push notification al finalizar el lavado
                if (auditWorkflow.getUser() != 0) {
                    utils.PushNotificationTimer.schedulePushNotification(use.getTransactionId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            AuditWorkflow audit = new AuditWorkflow(
                "ERROR Generating Machine Use ",
                AuditWorkflow.Status.ERROR,
                use.getTransactionId()
            );
            audit.save();
        }
    }
}
