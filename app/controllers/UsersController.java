package controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import dto.support.SendSupportMessageParameters;
import global.APIException;
import global.APIException.APIErrors;
import global.ErrorMessage;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import models.AccreditationLog;
import models.Card;
import models.Part.PartState;
import models.Unit;
import models.User;
import org.json.JSONException;
import org.json.JSONObject;
import play.i18n.Messages;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import services.ContactSupportService;
import utils.email.EmailService;
import views.html.generalMessage;

@ErrorMessage
@global.LoggingMessage
@Tx
@Security.Authenticated(Secured.class)
public class UsersController extends AbstractController {

    @Tx
    public Promise<Result> accreditation() throws APIException {
        JsonNode body = request().body().asJson();

        String uid = body.has("uuid") ? body.get("uuid").asText() : null;
        String emailAddress = body.has("emailAddress") ? body.get("emailAddress").asText() : null;
        String alias = body.has("alias") ? body.get("alias").asText() : null;

        if (uid == null || emailAddress == null) throw APIException.raise(
            APIErrors.MISSING_PARAMETERS
        );

        Collection<AccreditationLog> acc = AccreditationLog.find(emailAddress);

        if (acc.size() >= 5) {
            AccreditationLog acc_log = new AccreditationLog(
                emailAddress,
                new Date(),
                false,
                "ACCREDITATION_RETRIES_EXCEEDED:" + emailAddress
            );
            acc_log.save();

            JSONObject respJson = new JSONObject();

            try {
                respJson.put("result_code", "108");
                respJson.put("result_message", "ACCREDITATION_RETRIES_EXCEEDED");
                respJson.put("result_detail", "ACCREDITATION_RETRIES_EXCEEDED");
            } catch (JSONException e) {}

            return json(respJson.toString());
        }

        Card card = new Card();

        if (uid != null && !uid.equals("")) {
            if (uid.length() > 1 && !uid.startsWith("0x") && !uid.startsWith("0X")) uid =
                "0x" + uid;
        }

        card = Card.findByUID(uid);
        if (card != null) {
            card.setAlias(alias);
            card.update();
        } else {
            AccreditationLog acc_log = new AccreditationLog(
                emailAddress,
                new Date(),
                false,
                "CARD_NOT_FOUND:" + uid
            );
            acc_log.save();

            JSONObject respJson = new JSONObject();

            try {
                respJson.put("result_code", "10");
                respJson.put("result_message", "ex_card_not_found");
                respJson.put("result_detail", "CARD_NOT_FOUND");
            } catch (JSONException e) {}

            return json(respJson.toString());
        }

        User usr = new User();
        usr = usr.findByEmailAddress(emailAddress);

        if (usr == null) {
            AccreditationLog acc_log = new AccreditationLog(
                emailAddress,
                new Date(),
                false,
                "USER_NOT_FOUND:" + emailAddress
            );
            acc_log.save();

            JSONObject respJson = new JSONObject();

            try {
                respJson.put("result_code", "3");
                respJson.put("result_message", "USER_NOT_FOUND");
                respJson.put("result_detail", "USER_NOT_FOUND");
            } catch (JSONException e) {}

            return json(respJson.toString());
        }

        Unit unit = card.getUnit();
        User userUnit = new User();

        if (unit != null) {
            userUnit = unit.getOwner();
        } else {
            AccreditationLog acc_log = new AccreditationLog(
                emailAddress,
                new Date(),
                false,
                "UNIT_NOT_FOUND:" + unit
            );
            acc_log.save();

            JSONObject respJson = new JSONObject();

            try {
                respJson.put("result_code", "8");
                respJson.put("result_message", "UNIT_NOT_FOUND");
                respJson.put("result_detail", "UNIT_NOT_FOUND");
            } catch (JSONException e) {}

            return json(respJson.toString());
        }

        if (unit.getBlockedAt() != null) {
            throw APIException
                .raise(APIErrors.BLOCKED_UNIT)
                .setDetailMessage(Messages.get("BLOCKED_UNIT"));
        }

        String error = "";
        if (card.getState().equals(PartState.ACTIVE)) {
            if (userUnit != null) {
                // verifica si el dueño de la unit es el usuario a acreditar
                if (userUnit.equals(usr)) {
                    if (!unit.isAccredited()) {
                        unit.setAccredited(true);
                        unit.update();
                    }
                } else {
                    // throw
                    // APIException.raise(APIErrors.UNIT_INVALID_OWNER).setDetailMessage(Messages.get("INVALID_OWNER"));
                    unit.setOwner(usr);
                    unit.setAccredited(true);
                    unit.update();

                    try {
                        String server = request().host();
                        String link =
                            "http://" +
                            server +
                            "/api/v1/unit/block?param1=" +
                            unit.getId() +
                            "&param2=" +
                            userUnit.getEmailAddress();
                        String bodyMsj =
                            "Le notificamos que un nuevo usuario se ha acreditado con su número de tarjeta " +
                            card.getUuid().split("x")[1].toUpperCase() +
                            " correspondiente a la unidad de la dirección " +
                            unit.getDireccion() +
                            "." +
                            " Si usted entiende que se trata de un error o hurto tenga bien comenzar un proceso de reclamo o denuncia llamando al número 20351108 o utilizando el siguiente link: " +
                            link +
                            " De lo contrario por favor ignore este mensaje. Gracias";

                        String body2 = generalMessage
                            .render(bodyMsj, userUnit.getEmailAddress())
                            .body();

                        EmailService.send(
                            userUnit.getEmailAddress(),
                            "LAVOMAT - Notificación",
                            body2
                        );
                    } catch (Exception e) {
                        throw APIException
                            .raise(APIErrors.CANNOT_SEND_MESSAGE)
                            .setDetailMessage(Messages.get("CANNOT_SEND_MESSAGE"));
                    }
                }
            } else {
                // VALIDAR
                unit.setOwner(usr);
                unit.setAccredited(true);
                unit.update();
            }
        } else {
            AccreditationLog acc_log = new AccreditationLog(
                emailAddress,
                new Date(),
                false,
                "INACTIVE_CARD"
            );
            acc_log.save();

            JSONObject respJson = new JSONObject();

            try {
                respJson.put("result_code", "100");
                respJson.put("result_message", "INACTIVE_CARD");
                respJson.put("result_detail", "INACTIVE_CARD");
            } catch (JSONException e) {}

            return json(respJson.toString());
        }

        JSONObject response = new JSONObject();

        try {
            ArrayList<JSONObject> list = new ArrayList<JSONObject>();

            response.put("accredited", "SUCCESS");

            JSONObject unitJson = new JSONObject();
            unitJson.put("unit_id", unit.getId());
            unitJson.put("unit_address", unit.getDireccion());
            unitJson.put("assigned_card", unit.getAssignedUUID(usr));
            unitJson.put(
                "owner_id",
                unit.getOwner().getFirstName() + " " + unit.getOwner().getLastName()
            );
            unitJson.put("building_id", unit.getBuilding().getId());
            unitJson.put("building_name", unit.getBuilding().getName());
            unitJson.put("WashersCount", unit.getBuilding().getWashersCount());
            list.add(unitJson);
            response.put("Unit", list);
        } catch (JSONException e) {}

        return json(response.toString());
    }

    @Tx
    public Promise<Result> checkAccreditation(String emailAddress) throws APIException {
        boolean accredited = false;
        JsonNode body = request().body().asJson();

        // String emailAddress = body.has("emailAddress") ?
        // body.get("emailAddress").asText() : null;

        if (emailAddress == null) throw APIException.raise(APIErrors.MISSING_PARAMETERS);

        User usr = new User();
        usr = usr.findByEmailAddress(emailAddress);

        if (usr == null) {
            throw APIException
                .raise(APIErrors.USER_NOT_FOUND)
                .setDetailMessage(Messages.get("user_not_found"));
        }

        Unit u = new Unit();
        List<Unit> unit_list = u.getAccreditatedUnitsByOwnerId(usr);

        if (unit_list.size() > 0) {
            // esta acreditado en alguna unidad
            accredited = true;
        }

        JSONObject response = new JSONObject();

        try {
            if (accredited) {
                ArrayList<JSONObject> list = new ArrayList<JSONObject>();

                response.put("accredited", "SUCCESS");

                for (Unit uni : unit_list) {
                    JSONObject unitJson = new JSONObject();
                    unitJson.put("unit_id", uni.getId());
                    unitJson.put("unit_address", uni.getDireccion());
                    unitJson.put("assigned_card", uni.getAssignedUUID(usr));
                    unitJson.put(
                        "owner_id",
                        uni.getOwner().getFirstName() + " " + uni.getOwner().getLastName()
                    );
                    unitJson.put("building_id", uni.getBuilding().getId());
                    unitJson.put("building_name", uni.getBuilding().getName());
                    unitJson.put("WashersCount", uni.getBuilding().getWashersCount());
                    list.add(unitJson);
                    response.put("Unit", list);
                }
            } else {
                response.put("accredited", "User no accredited");
            }
        } catch (JSONException e) {}

        play.Logger.debug("RESPONSE>>> " + response.toString());

        return json(response.toString());
    }

    @Tx
    public Promise<Result> sendSupportMessage(String action, String uid, String emailAddress)
        throws APIException, JSONException {
        SendSupportMessageParameters params = new SendSupportMessageParameters(
            action,
            uid,
            emailAddress
        )
            .validate();

        JSONObject response = new JSONObject();

        ContactSupportService service = new ContactSupportService();
        try {
            if (params.requestNewCard()) {
                service.requestNewCard(params);
            } else if (params.requestReleaseCard()) {
                service.requestCardRelease(params);
            } else if (params.requestBlockCard()) {
                service.requestCardBlock(params);

                Card card = params.getCard();
                if (card != null) {
                    card.setState(PartState.INACTIVE);
                    card.update();
                }
            }

            response.put("emailSent", "OK");
        } catch (Exception ex) {
            ex.printStackTrace();

            response.put("emailSent", "ERROR");
        }

        return json(response.toString());
    }
}
