package models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import global.APIException;
import global.APIException.APIErrors;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import play.i18n.Messages;
import play.mvc.Http.Request;

@Entity
public class GroupInvite extends Model<GroupInvite> implements Comparable<GroupInvite> {

    @Id
    @GeneratedValue
    private int id;

    private String inviteKey = "";
    private boolean waitingForAcceptance = false;
    private Date sentDate;
    private Date acceptanceDate;

    @OneToOne
    private User user;

    private String status;

    @OneToOne(fetch = FetchType.LAZY)
    private WashingGroup group;

    public static Finder<Integer, GroupInvite> find = new Finder<Integer, GroupInvite>(
        Integer.class,
        GroupInvite.class
    );

    public GroupInvite() {}

    public GroupInvite(User user, WashingGroup group, String status) {
        this.sentDate = new Date();
        this.user = user;
        this.group = group;
        this.status = status;
    }

    public String waitForAcceptance() {
        String key = UUID.randomUUID().toString();
        this.inviteKey = key;
        this.waitingForAcceptance = true;
        this.save();

        return key;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public WashingGroup getGroup() {
        return this.group;
    }

    public void setGroup(WashingGroup g) {
        this.group = g;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User u) {
        this.user = u;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getSentDate() {
        return this.sentDate;
    }

    public void setAcceptanceDate(Date date) {
        this.acceptanceDate = date;
    }

    public Date getAcceptanceDate() {
        return this.acceptanceDate;
    }

    public void setSentDate(Date date) {
        this.sentDate = date;
    }

    public boolean isWaitingForAcceptance() {
        return waitingForAcceptance;
    }

    public void setWaitingForAceptance(boolean waitingForAcceptance) {
        this.waitingForAcceptance = waitingForAcceptance;
    }

    public String getInviteKey() {
        return inviteKey;
    }

    public void setInivteKey(String key) {
        this.inviteKey = key;
    }

    public static GroupInvite findByUserAndGroup(User u, WashingGroup g) {
        DefaultQuery<GroupInvite> query = (DefaultQuery<GroupInvite>) find.query();

        query.getCriteria().add(Restrictions.eq("user", u));
        query.getCriteria().add(Restrictions.eq("group", g));

        return query.findUnique();
    }

    public static GroupInvite findById(int inviteId) {
        return find.byId(inviteId);
    }

    public static List<GroupInvite> findBySentDate(Date sentDate) {
        DefaultQuery<GroupInvite> query = (DefaultQuery<GroupInvite>) find.query();

        query.getCriteria().add(Restrictions.eq("sentDate", sentDate));

        return query.findList();
    }

    public static List<GroupInvite> findAll() {
        return find.all();
    }

    // MEJORAR CRITERIO
    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!GroupInvite.class.isAssignableFrom(obj.getClass())) return false;
        return ((GroupInvite) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(GroupInvite o) {
        if (o.id > this.id) return 1; else if (o.id < this.id) return -1; else return 0;
    }
}
