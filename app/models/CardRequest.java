package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import java.util.UUID;
import javax.persistence.*;

@Entity
@Table(indexes = { @Index(name = "CARDREQUEST_TOKEN", columnList = "token") })
public class CardRequest extends Model<CardRequest> implements Comparable<CardRequest> {

    @Id
    @GeneratedValue
    private int id;

    private String token;

    @ManyToOne(cascade = CascadeType.ALL)
    private Building building;

    private String firstName;

    private String lastName;

    private String phone;

    @ManyToOne(cascade = CascadeType.ALL)
    private Unit unit;

    private String tower;

    private String email;

    private String rejectReason;

    private Date creationDate;

    private Date acceptanceDate;

    private Date rejectionDate;

    private Boolean acceptanceRequest;

    private String userUnitInput;

    @ManyToOne(cascade = CascadeType.ALL)
    private Card card;

    public static Finder<Integer, CardRequest> find = new Finder<Integer, CardRequest>(
        Integer.class,
        CardRequest.class
    );

    public CardRequest() {}

    public CardRequest(
        Building building,
        String firstName,
        String lastName,
        String phone,
        Unit unit,
        String tower,
        String email
    ) {
        this(building, firstName, lastName, phone, unit, tower, email, null);
    }

    public CardRequest(
        Building building,
        String firstName,
        String lastName,
        String phone,
        Unit unit,
        String tower,
        String email,
        Card card
    ) {
        this.token = UUID.randomUUID().toString();
        this.building = building;
        this.firstName = firstName;
        this.lastName = lastName;
        this.phone = phone;
        this.email = email;
        this.unit = unit;
        this.tower = tower;
        this.acceptanceRequest = true;
        this.creationDate = new Date();
        this.card = card;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building b) {
        this.building = b;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Unit getUnit() {
        return unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public String getTower() {
        return tower;
    }

    public void setTower(String tower) {
        this.tower = tower;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public Date getAcceptanceDate() {
        return acceptanceDate;
    }

    public void setAcceptanceDate(Date acceptanceDate) {
        this.acceptanceDate = acceptanceDate;
    }

    public Date getDate() {
        return creationDate;
    }

    public void setDate(Date d) {
        this.creationDate = d;
    }

    public Date getRejectionDate() {
        return rejectionDate;
    }

    public void setRejectionDate(Date rejectionDate) {
        this.rejectionDate = rejectionDate;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Card getCard() {
        return card;
    }

    public void setCard(Card card) {
        this.card = card;
    }

    public Boolean isAcceptanceRequest() {
        return acceptanceRequest;
    }

    public void setAcceptanceRequest(Boolean acceptanceRequest) {
        this.acceptanceRequest = acceptanceRequest;
    }

    public String getUserUnitInput() {
        return this.userUnitInput;
    }

    public void setUserUnitInput(String userUnitInput) {
        this.userUnitInput = userUnitInput;
    }

    public static CardRequest findByToken(final String token) {
        if (token == null) {
            return null;
        }

        try {
            return find.query().eq("token", token).findUnique();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!CardRequest.class.isAssignableFrom(obj.getClass())) return false;
        return ((CardRequest) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(CardRequest o) {
        if (o.id > this.id) return 1; else if (o.id < this.id) return -1; else return 0;
    }
}
