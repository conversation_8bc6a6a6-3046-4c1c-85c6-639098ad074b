package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Arrays;
import java.util.List;
import javax.persistence.*;
import services.bill.CFEItem;

@Entity
@Table(name = "bill_item")
public class BillItem extends Model<BillItem> implements CFEItem, Comparable<BillItem> {

    public static final String MEASURE_UNIT_USE = "Usos";
    public static final String MEASURE_UNIT_BALANCE = "Sld";
    public static final String MEASURE_UNIT_CARD = "Tarj";
    public static final String MEASURE_WASHER_MACHINE = "Lava";
    public static final String MEASURE_DRYER_MACHINE = "Seca";
    public static final String MEASURE_SOAP = "Ltrs";
    public static final String MEASURE_SPARE_PARTS = "Repo";
    public static final String MEASURE_LABOUR = "Mano";
    public static final String MEASURE_BRANDING_ITEM = "Acc";

    /**
     *
     */
    private static final long serialVersionUID = -3493658269122218046L;

    @Id
    @GeneratedValue
    private int id;

    private int amount;

    @Transient
    private int discreditedAmount;

    private int itemReference;
    private String itemType;

    /**
     * Expected values {@value #MEASURE_UNIT_USE} {@value #MEASURE_UNIT_BALANCE}
     * {@value #MEASURE_UNIT_CARD}
     */
    private String itemMeasureUnit;

    /**
     * Define the numeric value of billing indicator. Expected
     * values:
     * 1: Exento de IVA
     * 2: Gravado a Tasa Mínima
     * 3: Gravado a Tasa Básica
     */
    private int itemIndDet;
    private String itemName;
    private Double itemUnitPrice;

    @ManyToOne
    private Bill bill;

    public BillItem() {}

    public BillItem(int quantity, double priceCompany, boolean appliesIVA) {
        double priceWithoutIVA = priceCompany / 1.22;
        ItemBillingIndicator ivaType = appliesIVA
            ? ItemBillingIndicator.GRAVADO_A_TASA_BASICA
            : ItemBillingIndicator.EXENTO_DE_IVA;

        this.itemType = "MACHINE_USE";
        this.itemMeasureUnit = new MachineUse().getUnidadMedida();
        this.itemName = new MachineUse().getNomItem();
        this.itemUnitPrice = priceWithoutIVA;
        this.itemIndDet = ivaType.getCode();
        this.amount = quantity;
    }

    public BillItem(MachineUse machineUse) {
        this.itemReference = machineUse.getId();
        this.itemType = "MACHINE_USE";
        this.itemMeasureUnit = machineUse.getUnidadMedida();
        this.itemName = machineUse.getNomItem();
        this.itemUnitPrice =
            machineUse.getPrecioUnitario() *
            (1 - (machineUse.getCard() != null ? machineUse.getCard().getDiscount() : 0));
        this.itemIndDet = machineUse.getIndDet();
        this.amount = machineUse.getCantidad();
    }

    public BillItem(CFEItem u, String itemType, int amount, double unitPrice, int indDet) {
        this.itemType = itemType;
        this.itemMeasureUnit = u.getUnidadMedida();
        this.itemName = u.getNomItem();
        this.itemUnitPrice = unitPrice;
        this.itemIndDet = indDet;
        this.amount = amount;
    }

    public BillItem(CFEItem u) {
        this.itemType = u.getItemType();
        this.itemMeasureUnit = u.getUnidadMedida();
        this.itemName = u.getNomItem();
        this.itemUnitPrice = u.getPrecioUnitario();
        this.itemIndDet = u.getIndDet();
        this.amount = 1;
    }

    public BillItem copy() {
        BillItem bi = new BillItem();

        bi.setItemType(this.itemType);
        bi.setItemMeasureUnit(this.itemMeasureUnit);
        bi.setItemName(this.itemName);
        bi.setItemUnitPrice(this.itemUnitPrice);
        bi.setItemIndDet(this.itemIndDet);
        bi.setAmount(this.amount);
        bi.setItemReference(this.itemReference);

        return bi;
    }

    private static Finder<Integer, BillItem> find = new Finder<Integer, BillItem>(
        Integer.class,
        BillItem.class
    );

    public int getId() {
        return id;
    }

    public static BillItem findById(int userId) {
        return find.byId(userId);
    }

    public int getAmount() {
        return amount;
    }

    public void setAmount(int amount) {
        this.amount = amount;
    }

    public int getItemReference() {
        return itemReference;
    }

    public void setItemReference(int itemReference) {
        this.itemReference = itemReference;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getItemMeasureUnit() {
        return itemMeasureUnit;
    }

    public boolean belongsToUse() {
        return BillItem.MEASURE_UNIT_USE.equals(itemMeasureUnit);
    }

    public boolean belongsToCard() {
        return BillItem.MEASURE_UNIT_CARD.equals(itemMeasureUnit);
    }

    public boolean belongsToBalance() {
        return BillItem.MEASURE_UNIT_BALANCE.equals(itemMeasureUnit);
    }

    public void setItemMeasureUnit(String itemMeasureUnit) {
        this.itemMeasureUnit = itemMeasureUnit;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Double getItemUnitPrice() {
        return itemUnitPrice;
    }

    public void setItemUnitPrice(Double itemUnitPrice) {
        this.itemUnitPrice = itemUnitPrice;
    }

    public void setBill(Bill bill) {
        this.bill = bill;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!BillItem.class.isAssignableFrom(obj.getClass())) return false;
        return ((BillItem) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(BillItem o) {
        if (o.id > this.id) return 1; else if (o.id < this.id) return -1; else return 0;
    }

    @Override
    public int getIndDet() {
        return itemIndDet;
    }

    @Override
    public String getNomItem() {
        return itemName;
    }

    @Override
    public int getCantidad() {
        return amount;
    }

    @Override
    public String getUnidadMedida() {
        return itemMeasureUnit;
    }

    @Override
    public double getPrecioUnitario() {
        return itemUnitPrice;
    }

    public void setItemIndDet(int itemIndDet) {
        this.itemIndDet = itemIndDet;
    }

    public ItemBillingIndicator getItemIndDet() {
        return ItemBillingIndicator.valueOf(this.itemIndDet);
    }

    public int getDiscreditedAmount() {
        return discreditedAmount;
    }

    public void setDiscreditedAmount(int discreditedAmount) {
        this.discreditedAmount = discreditedAmount;
    }

    public Bill getBill() {
        return this.bill;
    }

    public static List<String> getMeasures() {
        List<String> unidades = Arrays.asList(
            MEASURE_UNIT_USE,
            MEASURE_UNIT_BALANCE,
            MEASURE_UNIT_CARD,
            MEASURE_WASHER_MACHINE,
            MEASURE_DRYER_MACHINE,
            MEASURE_SOAP,
            MEASURE_SPARE_PARTS,
            MEASURE_LABOUR
        );

        return unidades;
    }
}
