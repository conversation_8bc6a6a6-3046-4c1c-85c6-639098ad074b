package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Entity
public class POS extends Model<POS> implements Comparable<POS> {

    @Id
    @GeneratedValue
    private int id;

    @OneToOne(cascade = CascadeType.ALL)
    private Machine machine;

    private String name;

    private String category;

    @ManyToOne(cascade = CascadeType.ALL)
    private Store store;

    private Date creationDate;

    private String details;

    private String externalID; // POS_STOREID_MACHINESSERIAL

    private boolean fixed_amount;

    private String url;

    private String mp_pos_id;

    private String qr_image;

    private String qr_template_document;

    private String qr_template_image;

    public static Finder<Integer, POS> find = new Finder<Integer, POS>(Integer.class, POS.class);

    public POS() {}

    public POS(Machine m, Store s, String det) {
        this.machine = m;
        this.name = m.getName();
        this.category = null;
        this.creationDate = new Date();
        this.details = det;
        this.fixed_amount = true;
        this.store = s;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Machine getMachine() {
        return machine;
    }

    public void setMachine(Machine m) {
        this.machine = m;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCatgory(String c) {
        this.category = c;
    }

    public Store getStore() {
        return store;
    }

    public void setStore(Store s) {
        this.store = s;
    }

    public Date getDate() {
        return creationDate;
    }

    public void setDate(Date d) {
        this.creationDate = d;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getExternalId() {
        return externalID;
    }

    public void setExternalId(String id) {
        this.externalID = id;
    }

    public boolean getFixedAmount() {
        return fixed_amount;
    }

    public String getURL() {
        return url;
    }

    public void setURL(String u) {
        this.url = u;
    }

    public String getMPPosId() {
        return mp_pos_id;
    }

    public void setMPPosId(String u) {
        this.mp_pos_id = u;
    }

    public String getQrImage() {
        return qr_image;
    }

    public void setQrImage(String u) {
        this.qr_image = u;
    }

    public String getQrTemplateImage() {
        return qr_template_image;
    }

    public void setQrTemplateImage(String u) {
        this.qr_template_image = u;
    }

    public String getQrTemplateDoc() {
        return qr_template_document;
    }

    public void setQrTemplateDoc(String u) {
        this.qr_template_document = u;
    }

    public static POS findById(int id) {
        return find.byId(id);
    }

    public static List<POS> findAll() {
        return find.all();
    }

    public static POS findByMachineSerial(String serial) {
        DefaultQuery<POS> query = (DefaultQuery<POS>) find.query();

        query.join("machine").eq("machine.serialNumber", serial);

        return query.findUnique();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!POS.class.isAssignableFrom(obj.getClass())) return false;
        return ((POS) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(POS o) {
        if (o.id > this.id) return 1; else if (o.id < this.id) return -1; else return 0;
    }
}
