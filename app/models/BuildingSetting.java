package models;

import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Entity
public class BuildingSetting extends BaseModel<BuildingSetting> {

    @Id
    @GeneratedValue
    private int id;

    private int preBlockedUses = 0;

    /**
     * Google Maps link to building location.
     * Initially useful for laundromats list page on assistant
     */
    private String googleMapsLink;
    /**
     * Description of {@link BuildingSetting#googleMapsLink }
     */
    private String googleMapsDescription;

    /**
     * Laundromat opening time to limit machine usage outside operating hours
     */
    private Date openingTime;

    /**
     * Laundromat closing time to limit machine usage outside operating hours
     */
    private Date closingTime;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "buildingSetting", fetch = FetchType.LAZY)
    private List<BuildingReportingSchedule> buildingReportingSchedules;

    @Override
    public int getId() {
        return this.id;
    }

    public boolean isPreBlockedUseEnabled() {
        return this.preBlockedUses > 0;
    }

    public int getPreBlockedUses() {
        return this.preBlockedUses;
    }

    public String getGoogleMapsLink() {
        return this.googleMapsLink;
    }

    public String getGoogleMapsDescription() {
        return this.googleMapsDescription;
    }

    public Date getOpeningTime() {
        return this.openingTime;
    }

    public Date getClosingTime() {
        return this.closingTime;
    }

    public List<BuildingReportingSchedule> getBuildingReportingSchedules() {
        return this.buildingReportingSchedules;
    }

    public void setPreBlockedUseEnabled(int preBlockedUses) {
        this.preBlockedUses = preBlockedUses;
    }

    public void setGoogleMapsDescription(String googleMapsDescription) {
        this.googleMapsDescription = googleMapsDescription;
    }

    public void setGoogleMapsLink(String googleMapsLink) {
        this.googleMapsLink = googleMapsLink;
    }

    public void setClosingTime(Date closingTime) {
        this.closingTime = closingTime;
    }

    public void setOpeningTime(Date openingTime) {
        this.openingTime = openingTime;
    }

    public void setBuildingReportingSchedules(
        List<BuildingReportingSchedule> buildingReportingSchedules
    ) {
        this.buildingReportingSchedules = buildingReportingSchedules;
    }
}
