package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import org.hibernate.criterion.Restrictions;

@Entity
public class ClosureRecord extends Model<ClosureRecord> {

    public enum ClosureRecordState {
        PENDING,
        SUCCEED,
        FAILED,
    }

    /**
     *
     */
    private static final long serialVersionUID = -3557047328270703103L;

    @Id
    @GeneratedValue
    private int id;

    private Date timestamp;

    @OneToOne
    private Building building;

    private String headline;

    private String result;

    @ManyToOne
    private Bill bill;

    @Enumerated(EnumType.STRING)
    private ClosureRecordState state;

    private static Finder<Integer, ClosureRecord> find = new Finder<Integer, ClosureRecord>(
        Integer.class,
        ClosureRecord.class
    );

    public ClosureRecord() {}

    public ClosureRecord(Building building) {
        this.timestamp = new Date();
        this.building = building;
        this.headline = "Cierre de edificio " + building.getName();
        this.state = ClosureRecordState.PENDING;
        this.save();
    }

    public static List<ClosureRecord> findAll() {
        return find.all();
    }

    public static List<ClosureRecord> find(
        Date from,
        Date to,
        int buildingId,
        String keyword,
        ClosureRecordState state
    ) {
        DefaultQuery<ClosureRecord> q = (DefaultQuery<ClosureRecord>) find.query();

        if (state != null) q.eq("state", state);

        if (from != null) q.ge("timestamp", from);

        if (to != null) q.le("timestamp", to);

        if (buildingId > 0) q
            .getCriteria()
            .createAlias("building", "building")
            .add(Restrictions.eq("building.id", buildingId));

        return q.findList();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getHeadline() {
        return headline;
    }

    public void setHeadline(String headline) {
        this.headline = headline;
    }

    public Bill getBill() {
        return bill;
    }

    public void setBill(Bill bill) {
        this.bill = bill;
    }

    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public ClosureRecordState getState() {
        return state;
    }

    public void setState(ClosureRecordState state) {
        this.state = state;
    }

    public void fail(String result) {
        this.setState(ClosureRecordState.FAILED);
        this.setResult(result);
        this.update();
    }

    public void success(String result) {
        this.setState(ClosureRecordState.SUCCEED);
        this.setResult(result);
        this.update();
    }
}
