package models;

public enum Currency {
    UI("UI", 2, (short) 9800),
    PYG("PYG", 600, (short) 4800),
    USD("USD", 840, (short) 2225),
    UYU("UYU", 858, (short) 0),
    OTHER("OTHER", 0, (short) 0);

    private final String isoCode;
    private final Integer isoNumber;
    private final Short bcuCode;

    Currency(String isoCode, Integer isoNumebr, Short bcuCode) {
        this.isoCode = isoCode;
        this.isoNumber = isoNumebr;
        this.bcuCode = bcuCode;
    }

    public String getIsoCode() {
        return this.isoCode;
    }

    public Integer getIsoNumber() {
        return this.isoNumber;
    }

    public Short getBcuCode() {
        return this.bcuCode;
    }

    public static Currency findByIsoNumber(Integer isoNumebr) {
        for (Currency currency : values()) {
            if (currency.isoNumber.equals(isoNumebr)) {
                return currency;
            }
        }

        return OTHER;
    }
}
