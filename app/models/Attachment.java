package models;

import java.io.FileOutputStream;
import java.io.IOException;

public class Attachment {

    private String name;
    private String extension;
    private String MIMEType;
    private byte[] payload;

    public Attachment(String name, String extension, String mIMEType, byte[] payload) {
        super();
        this.name = name;
        this.extension = extension;
        this.MIMEType = mIMEType;
        this.payload = payload;
    }

    public void saveFileToDisk() {
        String filePath = "./" + name + "." + extension;
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(payload);
        } catch (IOException ex) {}
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMIMEType() {
        return MIMEType;
    }

    public void setMIMEType(String mIMEType) {
        this.MIMEType = mIMEType;
    }

    public byte[] getPayload() {
        return payload;
    }

    public void setPayload(byte[] payload) {
        this.payload = payload;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }
}
