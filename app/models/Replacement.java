package models;

import com.play4jpa.jpa.models.DefaultQuery;
import java.util.List;
import javax.persistence.Entity;
import org.hibernate.criterion.Restrictions;
import play.mvc.Http.Request;

@Entity
public class Replacement extends Part {

    /**
     *
     */
    private static final long serialVersionUID = -4041396763453973555L;

    private String englishDescription;
    private int minimumStock;
    private int anualConsumption;
    private int requestPoint;
    private double unitPrice;
    private double uyPrice;
    private int quantity = 0;

    public Replacement() {
        super();
    }

    public Replacement(
        String name,
        String model,
        String description,
        PartState state,
        String serialNumber
    ) {
        super(name, model, description, state, serialNumber);
    }

    public static List<Part> findAll() {
        DefaultQuery<Part> query = (DefaultQuery<Part>) finder.query();

        query.getCriteria().add(Restrictions.eq("class", Replacement.class.getSimpleName()));

        return query.findList();
    }

    public static List<Replacement> findWithFilters(Request filters) {
        DefaultQuery<Replacement> query = (DefaultQuery<Replacement>) finder.query();

        query.getCriteria().add(Restrictions.eq("class", Replacement.class.getSimpleName()));

        return query.findList();
    }

    public String getEnglishDescription() {
        return englishDescription;
    }

    public void setEnglishDescription(String englishDescription) {
        this.englishDescription = englishDescription;
    }

    public int getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(int minimumStock) {
        this.minimumStock = minimumStock;
    }

    public int getAnualConsumption() {
        return anualConsumption;
    }

    public void setAnualConsumption(int anualConsumption) {
        this.anualConsumption = anualConsumption;
    }

    public int getRequestPoint() {
        return requestPoint;
    }

    public void setRequestPoint(int requestPoint) {
        this.requestPoint = requestPoint;
    }

    public double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public double getUyPrice() {
        return uyPrice;
    }

    public void setUyPrice(double uyPrice) {
        this.uyPrice = uyPrice;
    }

    public int getQuantity() {
        return this.quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }
}
