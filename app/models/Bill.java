package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import domains.billing.dto.Branch;
import global.APIException;
import global.APIException.APIErrors;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.*;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import play.db.jpa.JPA;
import services.bill.CFE;
import services.bill.CFEItem;
import services.bill.CFERecipient;

@Entity
public class Bill extends Model<Bill> implements Comparable<Bill>, CFE {

    public enum BillState {
        NEW,
        SENT,
        PAID,
        ERROR_DGI,
        CANCELLED,
        BELOW_MINIMUM,
    }

    public enum BillType {
        ETICK,
        EFACT,
        CREDITNOTE,
        DEBITNOTE,
        COLLECTION_RECEIPT,
        CANCEL_COLLECTION_RECEIPT,
        EFACTEXPORTATION,
    }

    public enum BillCollectionStatus {
        PENDING_COLLECTION,
        HOLDED,
        AVAILABLE,
        RECONCILED,
    }

    public static final double UI_VALUE = 5.79;
    public static final double MAX_UI_AMOUNT = 5000;

    public static final String BILL_TO_TYPE_BUILDING = "BUILDING";
    public static final String BILL_TO_TYPE_MISC = "MISC";
    public static final String BILL_TO_TYPE_UNIT = "UNIT";
    public static final String BILL_TO_TYPE_FINAL = "FINAL";

    public static final String DOC_TYPE_RUT = "2";
    public static final String DOC_TYPE_CI = "3";
    public static final String DOC_TYPE_OTHERS = "4";

    private static final long serialVersionUID = -3493658269122218046L;

    @Id
    @GeneratedValue
    private int id;

    @Enumerated(EnumType.STRING)
    private BillState state = BillState.NEW;

    @Enumerated(EnumType.STRING)
    private BillType billType;

    @Enumerated(EnumType.STRING)
    private BillCollectionStatus billCollectionStatus;

    private Date creationDate;

    @ManyToOne
    @JoinColumn(name = "transaction_id")
    private Transaction transaction;

    private boolean isMainBill = true;

    @ManyToOne
    private User debtCollector;

    private Date collectionDate;

    private double total;

    /**
     * Expected values {@value #BILL_TO_TYPE_MISC} {@value #BILL_TO_TYPE_BUILDING}
     * {@value #BILL_TO_TYPE_UNIT} {@value #BILL_TO_TYPE_FINAL}
     */
    private String billToType;
    private int billTo;

    private String serie;
    private Integer number;
    private String tipoDoc;
    private String paymentReference;

    private String recipientName;

    /**
     * Define the numeric type of document thar was provided by the bill recipient.
     * Expected values:
     * 1: NIE
     * 2: RUC (Uruguay)
     * 3: C.I. (Uruguay)
     * 4: Otros
     * 5: Pasaporte (todos los países)
     * 6: DNI (documento de identidad de Argentina, Brasil, Chile o Paraguay)
     * 7: NIFE: número de identificación fiscal extranjero (todos los países)
     */

    /**
     * Expected values {@value #DOC_TYPE_RUT} {@value #DOC_TYPE_CI} {@value #DOC_TYPE_OTHERS}
     */
    private String recipientTipoDoc;
    private String recipientRut;
    private String recipientAddress;
    private String recipientCity;
    private String recipientDepartment;
    private String recipientCountry;
    private String recipientCountryCode;

    private boolean creditNote;
    private String creditNoteReason;

    private boolean debitNote;

    @OneToOne
    private Bill billReference;

    private Date billedPeriodStart;
    private Date billedPeriodEnd;

    private String currency;

    /**
     * Define the numeric value that indicates how the bill is collected. Expected
     * values:
     * 1: Contando - {@link InvoicePaymentMethod#CASH}
     * 2: Credito - {@link InvoicePaymentMethod#CREDIT}
     */
    private String paymentMethod;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "bill")
    private List<BillItem> detail;

    @OneToMany(mappedBy = "bill")
    private List<ExternalSaleNotificationRecord> externalSaleNotificationRecords;

    @Transient
    private boolean hasCreditOrDebitNote;

    /**
     * Associated building who the bill belongs to for financial purpose.
     * This value is only set for transactions which come from the TOTEM or
     * MercadoPago QRs.
     */
    @ManyToOne
    private Building associatedRecipient;

    public Bill() {}

    public Bill(
        String recipientName,
        String recipientDocType,
        String recipientDoc,
        String billAddress,
        String billCity,
        String billDepartment,
        String billCC,
        String billCountry,
        List<BillItem> items,
        Date periodStart,
        Date periodEnd,
        Date timestamp,
        String currency,
        String paymentMethod
    ) {
        this.creationDate = timestamp;
        this.recipientTipoDoc = recipientDocType;
        this.recipientName = recipientName;
        this.recipientRut = recipientDoc;
        this.recipientAddress = billAddress;
        this.recipientCity = billCity;
        this.recipientCountry = billCountry;
        this.recipientCountryCode = billCC;
        this.recipientDepartment = billDepartment;
        this.billToType = BILL_TO_TYPE_MISC;
        items.forEach(item -> item.setBill(this));
        this.detail = items;
        this.setBillType(resolveBillType(this));
        this.billedPeriodStart = periodStart;
        this.billedPeriodEnd = periodEnd;
        this.total = getMontoTotal();
        this.currency = currency;
        this.paymentMethod = paymentMethod;
        this.billCollectionStatus = BillCollectionStatus.PENDING_COLLECTION;
    }

    public Bill(List<BillItem> items, Date timestamp, String currency, String paymentMethod) {
        this.creationDate = timestamp;
        this.billToType = BILL_TO_TYPE_FINAL;
        items.forEach(item -> item.setBill(this));
        this.detail = items;
        this.setBillType(resolveBillType(this));
        this.total = getMontoTotal();
        this.currency = currency;
        this.paymentMethod = paymentMethod;
        this.billCollectionStatus = BillCollectionStatus.PENDING_COLLECTION;
        this.creationDate = timestamp;
    }

    public Bill(Unit unit, List<BillItem> items, Date periodStart, Date periodEnd) {
        this.creationDate = new Date();
        this.recipientName = unit.getBillingName();
        this.recipientRut = unit.getDoc();
        this.recipientAddress = unit.getDireccion();
        this.recipientCity = unit.getCiudad();
        this.recipientCountry = unit.getPais();
        this.recipientCountryCode = unit.getCodPais();
        this.recipientDepartment = unit.getDepartamento();
        this.billTo = unit.getId();
        this.billToType = BILL_TO_TYPE_UNIT;
        items.forEach(item -> item.setBill(this));
        this.detail = items;
        this.setBillType(resolveBillType(this));
        this.billedPeriodStart = periodStart;
        this.billedPeriodEnd = periodEnd;
        this.total = getMontoTotal();
        this.paymentMethod = unit.getBuilding().getInvoicingMethod().getCode();
        this.billCollectionStatus = BillCollectionStatus.PENDING_COLLECTION;
    }

    /**
     *  Used for building as a recipient
     **/
    public Bill(Building building, List<BillItem> items, Date periodStart, Date periodEnd) {
        this.creationDate = new Date();
        this.recipientName = building.getBillingName();
        this.recipientRut = building.getDoc();
        this.recipientAddress = building.getDireccion();
        this.recipientCity = building.getCiudad();
        this.recipientCountry = building.getPais();
        this.recipientCountryCode = building.getCodPais();
        this.recipientDepartment = building.getDepartamento();
        this.billTo = building.getId();
        this.billToType = BILL_TO_TYPE_BUILDING;
        items.forEach(item -> item.setBill(this));
        this.detail = items;
        this.setBillType(resolveBillType(this));
        this.billedPeriodStart = periodStart;
        this.billedPeriodEnd = periodEnd;
        this.total = getMontoTotal();
        this.paymentMethod = building.getInvoicingMethod().getCode();
        this.billCollectionStatus = BillCollectionStatus.PENDING_COLLECTION;

        Administration admin = building.getAdministration();
        if (admin != null) {
            Date collectionDate = admin.getCollectionDate();
            User debtCollector = admin.getDebtCollector();

            if (debtCollector != null) this.debtCollector = debtCollector;

            if (collectionDate != null) this.collectionDate = collectionDate;
        }
    }

    public Bill generateCreditNote(String reason, boolean isTotal) {
        Bill creditNote = new Bill();
        creditNote.creationDate = new Date();
        creditNote.recipientName = this.getRecipientName();
        creditNote.recipientRut = this.getRecipientRut();
        creditNote.recipientAddress = this.getRecipientAddress();
        creditNote.recipientCity = this.getRecipientCity();
        creditNote.recipientCountry = this.getRecipientCountry();
        creditNote.recipientCountryCode = this.getRecipientCountryCode();
        creditNote.recipientDepartment = this.getRecipientDepartment();
        creditNote.recipientTipoDoc = this.getRecipientTipoDoc();
        creditNote.billTo = this.getBillTo();
        creditNote.billToType = this.getBillToType();

        if (isTotal) {
            creditNote.detail =
                this.getDetail()
                    .stream()
                    .map(billItem -> {
                        BillItem newBillItem = billItem.copy();
                        newBillItem.setBill(creditNote);

                        return newBillItem;
                    })
                    .collect(Collectors.toList());

            creditNote.total = getMontoTotal();
        } else {
            creditNote.detail = new ArrayList<>();
        }

        creditNote.setCreditNote(true);
        creditNote.setBillReference(this);
        creditNote.setCreditNoteReason(
            reason != null
                ? reason
                : "Anulación de factura " + this.getSerie() + "-" + this.getNumber()
        );
        creditNote.setBillType(BillType.CREDITNOTE);
        creditNote.setCurrency(this.getCurrency());
        creditNote.setAssociatedRecipient(this.getAssociatedRecipient());

        return creditNote;
    }

    public Bill generateDebitNote(String reason) {
        Bill debitNote = new Bill();
        debitNote.creationDate = new Date();
        debitNote.recipientName = this.getRecipientName();
        debitNote.recipientRut = this.getRecipientRut();
        debitNote.recipientAddress = this.getRecipientAddress();
        debitNote.recipientCity = this.getRecipientCity();
        debitNote.recipientCountry = this.getRecipientCountry();
        debitNote.recipientCountryCode = this.getRecipientCountryCode();
        debitNote.recipientDepartment = this.getRecipientDepartment();
        debitNote.recipientTipoDoc = this.getRecipientTipoDoc();
        debitNote.billTo = this.getBillTo();
        debitNote.billToType = this.getBillToType();

        debitNote.detail =
            this.getDetail()
                .stream()
                .map(billItem -> {
                    BillItem newBillItem = billItem.copy();
                    newBillItem.setBill(debitNote);

                    return newBillItem;
                })
                .collect(Collectors.toList());

        debitNote.total = getMontoTotal();

        debitNote.setDebitNote(true);
        debitNote.setBillReference(this);
        debitNote.setCreditNoteReason(
            reason != null
                ? reason
                : "Anulación de nota de credito " + this.getSerie() + "-" + this.getNumber()
        );
        debitNote.setBillType(BillType.DEBITNOTE);
        debitNote.setCurrency(this.getCurrency());
        debitNote.setAssociatedRecipient(this.getAssociatedRecipient());

        return debitNote;
    }

    public Bill generateCollectionReceipt() {
        if (!billType.equals(BillType.EFACT) && !billType.equals(BillType.ETICK)) return null;

        Bill receipt = new Bill();
        receipt.setBillType(BillType.COLLECTION_RECEIPT);

        receipt.creationDate = new Date();
        receipt.recipientName = this.getRecipientName();
        receipt.recipientRut = this.getRecipientRut();
        receipt.recipientAddress = this.getRecipientAddress();
        receipt.recipientCity = this.getRecipientCity();
        receipt.recipientCountry = this.getRecipientCountry();
        receipt.recipientCountryCode = this.getRecipientCountryCode();
        receipt.recipientDepartment = this.getRecipientDepartment();
        receipt.recipientTipoDoc = this.getRecipientTipoDoc();
        receipt.billTo = this.getBillTo();
        receipt.billToType = this.getBillToType();
        receipt.detail = this.getDetail();
        receipt.total = getMontoTotal();
        receipt.setBillReference(this);

        return receipt;
    }

    public Bill generateCancellationOfCollectionReceipt() {
        if (!billType.equals(BillType.COLLECTION_RECEIPT)) return null;

        Bill receipt = new Bill();
        receipt.setBillType(BillType.CANCEL_COLLECTION_RECEIPT);

        receipt.creationDate = new Date();
        receipt.recipientName = this.getRecipientName();
        receipt.recipientRut = this.getRecipientRut();
        receipt.recipientAddress = this.getRecipientAddress();
        receipt.recipientCity = this.getRecipientCity();
        receipt.recipientCountry = this.getRecipientCountry();
        receipt.recipientCountryCode = this.getRecipientCountryCode();
        receipt.recipientDepartment = this.getRecipientDepartment();
        receipt.recipientTipoDoc = this.getRecipientTipoDoc();
        receipt.billTo = this.getBillTo();
        receipt.billToType = this.getBillToType();
        receipt.detail = this.getDetail();
        receipt.total = getMontoTotal();
        receipt.setBillReference(this);

        return receipt;
    }

    private BillType resolveBillType(Bill bill) {
        return bill.isCreditNote()
            ? (
                bill.getMontoTotal() > UI_VALUE * MAX_UI_AMOUNT || bill.getRecipient().hasRut()
                    ? BillType.CREDITNOTE
                    : BillType.CREDITNOTE
            )
            : (bill.getRecipient().hasRut() ? BillType.EFACT : BillType.ETICK);
    }

    private static Finder<Integer, Bill> find = new Finder<Integer, Bill>(
        Integer.class,
        Bill.class
    );

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public static Bill findById(int id) {
        return find.byId(id);
    }

    public static Bill findById(int id, boolean throwExceptionWhenMissing) throws APIException {
        Bill bill = find.byId(id);

        if (bill == null && throwExceptionWhenMissing) {
            throw APIException.raise(APIErrors.BILL_NOT_FOUND);
        }

        return bill;
    }

    public static List<Bill> findByCollectorId(
        int userId,
        BillCollectionStatus status,
        int page,
        int pageSize
    ) {
        DefaultQuery<Bill> query = (DefaultQuery<Bill>) find.query();
        query
            .getCriteria()
            .createAlias("billReference", "bf", JoinType.LEFT_OUTER_JOIN)
            .add(
                Restrictions.and(
                    Restrictions.eq("billCollectionStatus", status),
                    Restrictions.eq("debtCollector.id", userId)
                )
            );

        List<Bill> bills = query.findPage(page, pageSize);

        Bill.loadCreditAndDebitNotes(bills);

        return bills;
    }

    public static List<Bill> lastBillsByUnitId(int unitId, BillState state, int limit) {
        DefaultQuery<Bill> query = (DefaultQuery<Bill>) find.query();
        query.getCriteria().add(Restrictions.eq("billTo", unitId));
        query
            .getCriteria()
            .add(
                Restrictions.or(
                    Restrictions.eq("billType", BillType.EFACT),
                    Restrictions.eq("billType", BillType.ETICK)
                )
            );

        query.getCriteria().add(Restrictions.eq("state", state));
        query.orderByDesc("creationDate");

        if (limit != 0) {
            query.setMaxRows(limit);
        }

        return query.findList();
    }

    public static List<Bill> findByAdministrationsAndStatus(
        List<Administration> admins,
        BillCollectionStatus status,
        int page,
        int pageSize
    ) {
        DefaultQuery<Bill> query = (DefaultQuery<Bill>) find.query();

        List<Integer> buildingsId = new ArrayList<Integer>();
        List<Integer> buildingUnitsId = new ArrayList<Integer>();

        try {
            for (Administration a : admins) {
                List<Building> bs = a.getBuildings();
                buildingsId.addAll(bs.stream().map(Building::getId).collect(Collectors.toList()));
                buildingUnitsId.addAll(
                    bs
                        .stream()
                        .flatMap(b -> b.getUnits().stream().map(u -> u.getId()))
                        .collect(Collectors.toList())
                );
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        query.getCriteria().add(Restrictions.eq("billCollectionStatus", status));

        if (buildingUnitsId.isEmpty()) buildingUnitsId.add(0);

        if (buildingsId.isEmpty()) buildingsId.add(0);

        query
            .getCriteria()
            .add(
                Restrictions.or(
                    Restrictions.and(
                        Restrictions.eq("billToType", BILL_TO_TYPE_BUILDING),
                        Restrictions.in("billTo", buildingsId)
                    ),
                    Restrictions.and(
                        Restrictions.eq("billToType", BILL_TO_TYPE_UNIT),
                        Restrictions.in("billTo", buildingUnitsId)
                    )
                )
            );

        if (buildingUnitsId.isEmpty() && buildingsId.isEmpty()) return new ArrayList<Bill>();

        List<Bill> bills = query.findPage(page, pageSize);

        Bill.loadCreditAndDebitNotes(bills);

        return bills;
    }

    public static List<Bill> loadCreditAndDebitNotes(List<Bill> bills) {
        List<Integer> cancelledBillsIds = bills
            .stream()
            .filter(bill -> bill.getState().equals(BillState.CANCELLED))
            .map(Bill::getId)
            .collect(Collectors.toList());

        if (cancelledBillsIds.isEmpty()) {
            return bills;
        }

        List<Bill> creditOrDebitNotes = (List<Bill>) JPA
            .em()
            .createQuery("SELECT b FROM models.Bill b WHERE b.billReference.id IN :ids")
            .setParameter("ids", cancelledBillsIds)
            .getResultList();

        for (Bill bill : bills) {
            Bill childBill = creditOrDebitNotes
                .stream()
                .filter(note -> note.getParentBill().getId() == bill.getId())
                .findFirst()
                .orElse(null);

            if (childBill == null) continue;

            // Check child bill state?
            bill.setHasCreditOrDebitNote(true);
        }

        return bills;
    }

    public static List<Bill> find(
        int buildingId,
        int administrationId,
        Date from,
        Date to,
        int page,
        int number,
        String serie,
        String type,
        String mode,
        int pageSize,
        String collectionStatus,
        int collector,
        String[] states,
        String billOrigin,
        String order,
        String direction
    ) throws APIException {
        DefaultQuery<Bill> query = (DefaultQuery<Bill>) find.query();

        if (buildingId > -1) {
            Building building = Building.findById(buildingId);

            if (building == null) {
                throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
            }

            List<Integer> units = building
                .getUnits()
                .stream()
                .map(Unit::getId)
                .collect(Collectors.toList());

            query
                .getCriteria()
                .add(
                    Restrictions.or(
                        Restrictions.and(
                            Restrictions.eq("billToType", BILL_TO_TYPE_BUILDING),
                            Restrictions.eq("billTo", buildingId)
                        ),
                        Restrictions.and(
                            Restrictions.eq("billToType", BILL_TO_TYPE_UNIT),
                            Restrictions.in("billTo", units.isEmpty() ? Arrays.asList(-1) : units)
                        )
                    )
                );
        }

        if (administrationId > -1) {
            Administration administration = Administration.findById(administrationId);

            if (administration == null) {
                throw APIException.raise(APIErrors.ADMINISTRATION_NOT_FOUND);
            }

            List<Integer> buildingsId = new ArrayList<Integer>();
            List<Integer> buildingUnitsId = new ArrayList<Integer>();
            try {
                List<Building> bs = administration.getBuildings();
                buildingsId =
                    administration
                        .getBuildings()
                        .stream()
                        .map(Building::getId)
                        .collect(Collectors.toList());
                buildingUnitsId =
                    administration
                        .getBuildings()
                        .stream()
                        .flatMap(b -> b.getUnits().stream().map(Unit::getId))
                        .collect(Collectors.toList());
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (!buildingsId.isEmpty() && !buildingUnitsId.isEmpty()) {
                query
                    .getCriteria()
                    .add(
                        Restrictions.or(
                            (
                                Restrictions.and(
                                    Restrictions.eq("billToType", BILL_TO_TYPE_BUILDING),
                                    Restrictions.in("billTo", buildingsId)
                                )
                            ),
                            (
                                Restrictions.and(
                                    Restrictions.eq("billToType", BILL_TO_TYPE_UNIT),
                                    Restrictions.in("billTo", buildingUnitsId)
                                )
                            )
                        )
                    );
            }
        }

        SimpleDateFormat fromSdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        SimpleDateFormat untilSdf = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            if (from != null) {
                query.ge("creationDate", sdf.parse(fromSdf.format(from)));
            }

            if (to != null) {
                query.le("creationDate", sdf.parse(untilSdf.format(to)));
            }

            if (number > 0) {
                query.eq("number", number);
            }

            if (serie != "" && !serie.isEmpty()) {
                query.eq("serie", serie);
            }

            if (type != null && !type.isEmpty()) {
                query.eq("billType", BillType.valueOf(type));
            }

            if (mode != null && !mode.isEmpty()) {
                query.eq("billToType", mode);
            }

            if (collectionStatus != null && !collectionStatus.isEmpty()) {
                query.eq("billCollectionStatus", BillCollectionStatus.valueOf(collectionStatus));
            }

            if (collector > -1) {
                query.getCriteria().add(Restrictions.eq("debtCollector.id", collector));
            }

            if (states != null) {
                List<Enum> parsedStates = new ArrayList<Enum>();
                for (int i = 0; i < states.length; i++) {
                    parsedStates.add(BillState.valueOf(states[i]));
                }
                query.getCriteria().add(Restrictions.in("state", parsedStates));
            }

            if (billOrigin != null && !billOrigin.isEmpty()) {
                query.join("transactions").eq("transactions.origin", billOrigin);
            }

            if (order != null && !order.isEmpty()) {
                if (order.equals("transactions.origin")) {
                    query.join("transactions");
                }

                if (direction != null && !direction.isEmpty() && direction.equals("desc")) {
                    query.orderByDesc(order);
                } else {
                    query.orderByAsc(order);
                }
            }

            if (page == 0 || pageSize == 0) {
                query.findList();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return query.findPage(page, pageSize);
    }

    public static Bill findByRealIdentificators(String serie, Integer number, BillType billType) {
        DefaultQuery<Bill> query = (DefaultQuery<Bill>) find.query();

        query.eq("number", number);
        query.eq("serie", serie);
        query.eq("billType", billType);

        return query.findList().get(0);
    }

    public static Bill findByBillReference(Integer billReferenceId) {
        DefaultQuery<Bill> query = (DefaultQuery<Bill>) find.query();

        query.getCriteria().add(Restrictions.eq("billReference.id", billReferenceId));

        return query.findList().get(0);
    }

    public static List<String> getBillTypes() {
        List<String> billTypes = new ArrayList<String>();

        List<String> results = (List<String>) JPA
            .em()
            .createQuery("SELECT DISTINCT billType FROM models.Bill WHERE billType IS NOT NULL")
            .getResultList();
        for (Object s : results) billTypes.add(s.toString());

        return billTypes;
    }

    public static List<String> getDGINumbers() {
        List<String> dgiNumbers = new ArrayList<String>();

        List<Object> results = JPA
            .em()
            .createQuery(
                "SELECT DISTINCT serie, number FROM models.Bill WHERE serie IS NOT NULL and number IS NOT NULL GROUP BY serie, number"
            )
            .getResultList();

        for (Object s : results) {
            Object[] v = (Object[]) s;
            String value = v[0] + "- " + v[1];
            dgiNumbers.add(value);
        }

        return dgiNumbers;
    }

    public BillState getState() {
        return state;
    }

    public void setState(BillState state) {
        this.state = state;
    }

    public double getTotal() {
        return total;
    }

    public void setTotal(double total) {
        this.total = total;
    }

    public void setTotal(List<BillItem> discreditedItems, List<MachineUse> usesNotAccredited) {
        this.total = 0;
        if (usesNotAccredited != null && usesNotAccredited.size() > 0) this.total +=
            this.getMontoParcialByUses(usesNotAccredited);

        if (discreditedItems != null && discreditedItems.size() > 0) this.total +=
            this.getMontoParcialByItems(discreditedItems);
    }

    public List<BillItem> filterDetail(
        List<BillItem> discreditedItems,
        List<MachineUse> usesNotAccredited
    ) {
        List<BillItem> currentDetail = this.getDetail();
        List<BillItem> finalDetail = new ArrayList<BillItem>();
        List<BillItem> restDetail = new ArrayList<BillItem>();
        if (currentDetail != null && !currentDetail.isEmpty()) {
            for (BillItem item : currentDetail) {
                if (usesNotAccredited != null && !usesNotAccredited.isEmpty()) {
                    Optional<MachineUse> use = usesNotAccredited
                        .stream()
                        .filter(x -> x.getId() == item.getItemReference())
                        .findFirst();
                    if (use.isPresent()) finalDetail.add(item); else restDetail.add(item); // if there are not canceled machine use, it need be in the old bill
                } else if (discreditedItems != null && !discreditedItems.isEmpty()) {
                    // always get one, because billitems are grouped
                    Optional<BillItem> optionalItem = discreditedItems
                        .stream()
                        .filter(x ->
                            x.getItemName().equals(item.getItemName()) &&
                            Double.compare(x.getItemUnitPrice(), item.getItemUnitPrice()) == 0
                        )
                        .findFirst();
                    if (optionalItem.isPresent()) {
                        BillItem currentItem = optionalItem.get();
                        if (currentItem.getDiscreditedAmount() > 0) {
                            int newCurrentDiscreditedAmount = 0;

                            if (
                                currentItem.getDiscreditedAmount() >= item.getAmount()
                            ) newCurrentDiscreditedAmount =
                                currentItem.getDiscreditedAmount() - item.getAmount(); else {
                                // if there are amount yet, it need be in the old bill
                                BillItem restItem = item.copy();
                                restItem.setAmount(
                                    restItem.getAmount() - currentItem.getDiscreditedAmount()
                                );
                                restDetail.add(restItem);

                                item.setAmount(currentItem.getDiscreditedAmount());
                            }

                            finalDetail.add(item);
                            currentItem.setDiscreditedAmount(newCurrentDiscreditedAmount);
                        }
                    }
                }
            }
        }

        this.setDetail(finalDetail);

        if (
            discreditedItems != null &&
            usesNotAccredited != null &&
            discreditedItems.isEmpty() &&
            usesNotAccredited.isEmpty()
        ) restDetail = currentDetail;

        return restDetail;
    }

    public void setCreditNote(boolean creditNote) {
        this.creditNote = creditNote;
    }

    public void setCreditNoteReason(String creditNoteReason) {
        this.creditNoteReason = creditNoteReason;
    }

    public void setBillReference(Bill billReference) {
        this.billReference = billReference;
    }

    public void setDetail(List<BillItem> items) {
        this.detail = items;
    }

    public List<BillItem> getDetail() {
        return detail;
    }

    public List<BillItem> getShortDetail(List<Integer> notAccreditedUsesId) {
        Map<String, Map<Double, List<BillItem>>> itemsGrouped = detail
            .stream()
            .collect(
                Collectors.groupingBy(
                    BillItem::getItemName,
                    Collectors.groupingBy(BillItem::getItemUnitPrice)
                )
            );

        List<BillItem> shortDetails = new ArrayList<BillItem>();

        for (String itemByName : itemsGrouped.keySet()) {
            Map<Double, List<BillItem>> itemsUnitPrices = itemsGrouped.get(itemByName);
            for (Double itemByUnitPrice : itemsUnitPrices.keySet()) {
                Optional<BillItem> billItem = itemsUnitPrices
                    .get(itemByUnitPrice)
                    .stream()
                    .findFirst();
                if (billItem.isPresent()) {
                    int amountSum = itemsUnitPrices
                        .get(itemByUnitPrice)
                        .stream()
                        .mapToInt(x -> x.getAmount())
                        .sum();
                    billItem.get().setAmount(amountSum);
                    if (billItem.get().getItemMeasureUnit().equals(BillItem.MEASURE_UNIT_USE)) {
                        int discreditedAmount = (int) (
                            itemsUnitPrices
                                .get(itemByUnitPrice)
                                .stream()
                                .filter(x -> notAccreditedUsesId.contains(x.getItemReference()))
                                .count()
                        );
                        billItem.get().setDiscreditedAmount(discreditedAmount);
                    } else billItem.get().setDiscreditedAmount(0);
                    shortDetails.add(billItem.get());
                }
            }
        }

        return shortDetails;
    }

    public int getBillTo() {
        return billTo;
    }

    public void setBillTo(int billTo) {
        this.billTo = billTo;
    }

    public String getBillToType() {
        return billToType;
    }

    public boolean belongsToBuilding() {
        return BILL_TO_TYPE_BUILDING.equals(billToType);
    }

    public boolean belongsToUnit() {
        return BILL_TO_TYPE_UNIT.equals(billToType);
    }

    public boolean belongsToAnonymous() {
        return BILL_TO_TYPE_MISC.equals(billToType) || BILL_TO_TYPE_FINAL.equals(billToType);
    }

    public void setBillToType(String billToType) {
        this.billToType = billToType;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getRecipientRut() {
        return recipientRut;
    }

    public void setRecipientRut(String recipientRut) {
        this.recipientRut = recipientRut;
    }

    public String getRecipientAddress() {
        return recipientAddress;
    }

    public void setRecipientAddress(String recipientAddress) {
        this.recipientAddress = recipientAddress;
    }

    public String getRecipientCity() {
        return recipientCity;
    }

    public void setRecipientCity(String recipientCity) {
        this.recipientCity = recipientCity;
    }

    public String getRecipientDepartment() {
        return recipientDepartment;
    }

    public void setRecipientDepartment(String recipientDepartment) {
        this.recipientDepartment = recipientDepartment;
    }

    public String getRecipientCountry() {
        return recipientCountry;
    }

    public User getDebtCollector() {
        return debtCollector;
    }

    public void setDebtCollector(User debtCollector) {
        this.debtCollector = debtCollector;
    }

    public Date getCollectionDate() {
        return collectionDate;
    }

    public void setCollectionDate(Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    public void setRecipientCountry(String recipientCountry) {
        this.recipientCountry = recipientCountry;
    }

    public String getRecipientCountryCode() {
        return recipientCountryCode;
    }

    public Date getBilledPeriodStart() {
        return billedPeriodStart;
    }

    public void setBilledPeriodStart(Date billedPeriodStart) {
        this.billedPeriodStart = billedPeriodStart;
    }

    public Date getBilledPeriodEnd() {
        return billedPeriodEnd;
    }

    public void setBilledPeriodEnd(Date billedPeriodEnd) {
        this.billedPeriodEnd = billedPeriodEnd;
    }

    public void setRecipientCountryCode(String recipientCountryCode) {
        this.recipientCountryCode = recipientCountryCode;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public void setTipoDoc(String tipoDoc) {
        this.tipoDoc = tipoDoc;
    }

    public BillType getBillType() {
        return billType;
    }

    public void setBillType(BillType billType) {
        this.billType = billType;
    }

    public BillCollectionStatus getBillCollectionStatus() {
        return billCollectionStatus;
    }

    public void setBillCollectionStatus(BillCollectionStatus billCollectionStatus) {
        this.billCollectionStatus = billCollectionStatus;
    }

    public String getCurrency() {
        return this.currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public void setCurrency(Currency currency) {
        this.currency = currency.toString();
    }

    public String getPaymentReference() {
        return this.paymentReference;
    }

    public void setPaymentReference(String reference) {
        this.paymentReference = reference;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public void setCashPaymentMethod() {
        this.paymentMethod = InvoicePaymentMethod.CASH.getCode();
    }

    public void setCreditPaymentMethod() {
        this.paymentMethod = InvoicePaymentMethod.CREDIT.getCode();
    }

    public boolean hasCreditOrDebitNote() {
        return this.hasCreditOrDebitNote;
    }

    public void setHasCreditOrDebitNote(boolean hasCreditOrDebitNote) {
        this.hasCreditOrDebitNote = hasCreditOrDebitNote;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!Bill.class.isAssignableFrom(obj.getClass())) return false;
        return ((Bill) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(Bill o) {
        if (o.id > this.id) return 1; else if (o.id < this.id) return -1; else return 0;
    }

    @Override
    @Transient
    public double getMontoNetoIvaTasaBasica() {
        return getMontoTotal() / 1.22;
    }

    @Override
    @Transient
    public double getMontoIvaTasaBasica() {
        return getMontoNetoIvaTasaBasica() * 0.22;
    }

    @Override
    @Transient
    public double getMontoTotal() {
        double total = 0;

        for (BillItem item : getDetail()) {
            total += (item.getPrecioUnitario() * item.getCantidad());
        }

        if (isTaxFree()) {
            return total;
        }

        return total * 1.22;
    }

    public static double previewMontoTotal(List<BillItem> billItems) {
        double total = 0;

        for (BillItem item : billItems) {
            total += (item.getPrecioUnitario() * item.getCantidad());
        }

        if (
            billItems
                .stream()
                .allMatch(item -> item.getItemIndDet() == ItemBillingIndicator.EXENTO_DE_IVA)
        ) {
            return total;
        }

        return total * 1.22;
    }

    public boolean isTaxFree() {
        return getDetail()
            .stream()
            .allMatch(item -> item.getItemIndDet() == ItemBillingIndicator.EXENTO_DE_IVA);
    }

    private double getMontoParcialByUses(List<MachineUse> parcialUses) {
        double total = 0;

        List<BillItem> billItems = getDetail();

        for (MachineUse item : parcialUses) {
            Stream<BillItem> bis = billItems
                .stream()
                .filter(x -> x.getItemReference() == item.getId());
            if (bis != null) {
                Optional<BillItem> bi = bis.findFirst();
                if (bi != null && bi.isPresent()) total +=
                    (bi.get().getPrecioUnitario() * bi.get().getCantidad());
            }
        }

        return total * 1.22;
    }

    private double getMontoParcialByItems(List<BillItem> discreditedItems) {
        double total = 0;

        for (BillItem item : discreditedItems) {
            total += (item.getPrecioUnitario() * item.getDiscreditedAmount());
        }

        return total * 1.22;
    }

    @Override
    @Transient
    public int getCantidadLineas() {
        return detail.size();
    }

    @Override
    @Transient
    public List<? extends CFEItem> getItems() {
        return detail;
    }

    @Override
    @Transient
    public CFERecipient getRecipient() {
        switch (billToType) {
            case BILL_TO_TYPE_UNIT:
                return Unit.findById(getBillTo());
            case BILL_TO_TYPE_BUILDING:
                return Building.findById(getBillTo());
        }

        return new CFERecipient() {
            @Override
            public int getTipoDoc() {
                return Bill.this.getRecipientTipoDoc() != null
                    ? Integer.parseInt(Bill.this.getRecipientTipoDoc())
                    : -1;
            }

            @Override
            public String getBillingName() {
                return Bill.this.getRecipientName();
            }

            @Override
            public String getDoc() {
                return Bill.this.getRecipientRut();
            }

            @Override
            public boolean hasRut() {
                return Bill.this.getRecipientRut() != null;
            }

            @Override
            public String getDireccion() {
                return Bill.this.getRecipientAddress();
            }

            @Override
            public String getCiudad() {
                return Bill.this.getRecipientCity();
            }

            @Override
            public String getDepartamento() {
                return Bill.this.getRecipientDepartment();
            }

            @Override
            public String getPais() {
                return Bill.this.getRecipientCountry();
            }

            @Override
            public String getCodPais() {
                return Bill.this.getRecipientCountryCode();
            }
        };
    }

    @Override
    public String getReference() {
        return getId() + "";
    }

    @Override
    public boolean isCreditNote() {
        return creditNote;
    }

    @Override
    public boolean isDebitNote() {
        return debitNote;
    }

    @Override
    public boolean isCollectionReceipt() {
        return billType.equals(BillType.COLLECTION_RECEIPT);
    }

    @Override
    public boolean isCancelCollectionReceipt() {
        return billType.equals(BillType.CANCEL_COLLECTION_RECEIPT);
    }

    @Override
    public String getCreditNoteReason() {
        return creditNoteReason;
    }

    @Override
    public CFE getBillReference() {
        return billReference;
    }

    @Override
    public String getSerie() {
        return serie;
    }

    @Override
    public Integer getNumber() {
        return number;
    }

    @Override
    public String getTipoDoc() {
        return tipoDoc;
    }

    @Override
    public Date getTimestamp() {
        return getCreationDate();
    }

    @Override
    public Date getPeriodoDesde() {
        return billedPeriodStart;
    }

    @Override
    public Date getPeriodoHasta() {
        return billedPeriodEnd;
    }

    @Override
    public String getTipoMoneda() {
        return currency != null ? currency : "UYU";
    }

    @Override
    public String getFormaPago() {
        return this.paymentMethod != null
            ? this.paymentMethod
            : InvoicePaymentMethod.CREDIT.getCode();
    }

    public String getRecipientTipoDoc() {
        return recipientTipoDoc;
    }

    public void setRecipientTipoDoc(String recipientTipoDoc) {
        this.recipientTipoDoc = recipientTipoDoc;
    }

    public void setDebitNote(boolean debitNote) {
        this.debitNote = debitNote;
    }

    public Bill getParentBill() {
        return this.billReference;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public boolean isMainBill() {
        return this.isMainBill;
    }

    public void setMainBill(boolean mainBill) {
        this.isMainBill = mainBill;
    }

    public String getBillOrigin() {
        if (this.getTransaction() == null || this.getTransaction().getOrigin() == null) {
            return "";
        } else {
            return this.getTransaction().getOrigin();
        }
    }

    public List<ExternalSaleNotificationRecord> getExternalSaleNotificationRecords() {
        return this.externalSaleNotificationRecords;
    }

    public ExternalSaleNotificationRecord getLastExternalSaleNotificationRecord() {
        if (this.externalSaleNotificationRecords == null) {
            return null;
        }

        return this.externalSaleNotificationRecords.stream()
            .sorted(Comparator.comparing(ExternalSaleNotificationRecord::getCreatedAt).reversed())
            .findFirst()
            .orElse(null);
    }

    public Building getAssociatedRecipient() {
        return this.associatedRecipient;
    }

    public void setAssociatedRecipient(Building associatedRecipient) {
        this.associatedRecipient = associatedRecipient;
    }

    @Override
    public Branch getBranch() {
        // Firstly, try to get the intended associated recipient.
        Building building = this.getAssociatedRecipient();
        if (building != null) {
            return building.getBranch();
        }

        /**
         * if there is none, try to get the associated recipient based on the
         * {@link #billToType} and {@link billTo} attributes
         */
        return this.getRecipient().getBranch();
    }
}
