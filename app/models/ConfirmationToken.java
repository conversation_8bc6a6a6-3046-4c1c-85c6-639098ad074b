package models;

import com.play4jpa.jpa.models.*;
import java.util.*;
import javax.persistence.*;
import models.*;
import org.hibernate.criterion.Restrictions;

@Entity
@Table(indexes = { @Index(name = "CONFIRMATION_TOKEN_TOKEN", columnList = "token", unique = true) })
public class ConfirmationToken extends Model<ConfirmationToken> {

    @Id
    @GeneratedValue
    private int id;

    private Date createdAt;
    private Date expiresOn;
    private String token;
    private boolean deleted;

    @ManyToOne(cascade = { CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE })
    private User user;

    @Enumerated(EnumType.STRING)
    private TokenType type;

    private static final Finder<Integer, ConfirmationToken> find = new Finder<Integer, ConfirmationToken>(
        Integer.class,
        ConfirmationToken.class
    );

    public ConfirmationToken() {}

    public ConfirmationToken(TokenType type, User user) {
        this.type = type;
        this.createdAt = new Date();
        this.user = user;
        this.token = UUID.randomUUID().toString();
        this.deleted = false;

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, 15);
        Date expiresOn = calendar.getTime();
        this.expiresOn = expiresOn;
    }

    public boolean isValid() {
        return !this.deleted && new Date().before(this.expiresOn);
    }

    public static ConfirmationToken findByToken(String token) {
        DefaultQuery<ConfirmationToken> query = (DefaultQuery<ConfirmationToken>) find.query();
        query.eq("token", token);
        return query.findUnique();
    }

    public static ConfirmationToken getActiveConfirmationToken(int userId) {
        DefaultQuery<ConfirmationToken> query = (DefaultQuery<ConfirmationToken>) find.query();
        query.join("user").eq("user.id", userId);
        query.eq("deleted", false);
        return query.findUnique();
    }

    public static List<ConfirmationToken> getTokensToInvalidate() {
        DefaultQuery<ConfirmationToken> query = (DefaultQuery<ConfirmationToken>) find.query();
        query.lt("expiresOn", new Date());
        query.eq("deleted", false);
        return query.findList();
    }

    public static List<ConfirmationToken> getAllActiveTokens(int userId) {
        DefaultQuery<ConfirmationToken> query = (DefaultQuery<ConfirmationToken>) find.query();

        query.join("user").eq("user.id", userId);
        query.eq("deleted", false);

        return query.findList();
    }

    public void softDelete() {
        this.deleted = true;
    }

    public String getToken() {
        return this.token;
    }

    public User getUser() {
        return this.user;
    }
}
