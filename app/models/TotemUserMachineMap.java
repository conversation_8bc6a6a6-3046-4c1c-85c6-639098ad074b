package models;

import javax.persistence.*;

@Entity
@Table(
    indexes = {
        @Index(name = "TOTEM_USER", columnList = "totem_user_id, machine_id", unique = true),
    }
)
public class TotemUserMachineMap extends BaseModel<TotemUserMachineMap> {

    @Id
    @GeneratedValue
    private int id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "totem_user_id")
    private User totemUser;

    @ManyToOne(optional = false)
    @JoinColumn(name = "machine_id")
    private Machine machine;

    public TotemUserMachineMap(int totemUserId, int machineId) {
        this.totemUser = User.findById(totemUserId);
        this.machine = Machine.findById(machineId);
    }

    protected TotemUserMachineMap() {}

    @Override
    public int getId() {
        return this.id;
    }

    public User getTotemUser() {
        return totemUser;
    }

    public Machine getMachine() {
        return machine;
    }
}
