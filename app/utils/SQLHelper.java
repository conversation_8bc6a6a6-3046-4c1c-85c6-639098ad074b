package utils;

import models.MachineUseResult;

import java.util.Arrays;
import java.util.stream.Collectors;

import static models.MachineUse.ACCREDITED_RESULT;

public class SQLHelper {

    public static String getAccreditedMachineUseResultCodesForSql() {
        return getMachineUseResultCodesForSql(ACCREDITED_RESULT.toArray(new MachineUseResult[0]));
    }

    public static String getMachineUseResultCodesForSql(MachineUseResult... results) {
        return Arrays
            .stream(results)
            .map(result -> "'" + result.getCodeString() + "'")
            .collect(Collectors.joining(", ", "(", ")"));
    }
}
