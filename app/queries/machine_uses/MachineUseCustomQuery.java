package queries.machine_uses;

import models.Machine;
import models.MachineUse;
import models.MachineUseResult;
import play.db.jpa.JPA;
import utils.SQLHelper;

public class MachineUseCustomQuery {

    public MachineUse getLastMachineUseByMachine(
        Machine machine,
        MachineUseResult... results
    ) {
        try {
            return JPA
                .em()
                .createQuery(
                    "SELECT mu FROM models.MachineUse mu " +
                        "WHERE mu.result IN " +
                        SQLHelper.getMachineUseResultCodesForSql(results) + " " +
                        "AND mu.machine.id = :mid " +
                        "ORDER BY mu.timestamp DESC ",
                    MachineUse.class
                ).setParameter("mid", machine.getId())
                .setMaxResults(1)
                .getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }
}
