package queries;

import models.Part;
import org.hibernate.criterion.Restrictions;

/**
 * Examples: https://github.com/LAVOMAT/lm-app/pull/152
 *
 * @param <M> model class from model Part inherits
 */
public abstract class PartModelQuery<M extends Part> extends BaseFinderModelQuery<Part, M> {

    protected PartModelQuery(Class<M> modelClass) {
        super(Part.class);
        query().getCriteria().add(Restrictions.eq("class", modelClass.getSimpleName()));
    }
}
