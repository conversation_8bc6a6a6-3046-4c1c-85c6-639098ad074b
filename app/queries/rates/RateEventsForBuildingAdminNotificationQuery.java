package queries.rates;

import java.util.List;
import models.RateEvent;
import play.db.jpa.JPA;

public class RateEventsForBuildingAdminNotificationQuery extends RateEventQuery {

    public RateEventsForBuildingAdminNotificationQuery() {
        super();
    }

    public List<RateEvent> getRateEventsForBuildingAdminNotification(int deltaDays) {
        return JPA
            .em()
            .createQuery(
                "SELECT re1 " +
                "FROM RateEvent re1 " +
                "WHERE re1.validFrom >= NOW() " +
                "AND EXISTS ( " +
                "SELECT 1 " +
                "FROM RateEvent re2 " +
                "WHERE re2.rate.id = re1.rate.id " +
                "AND DATEDIFF(re1.validFrom, re2.validUntil) = 1 " +
                "AND re2.validFrom <= NOW() " +
                "AND re2.validUntil >= NOW() " +
                "AND DATEDIFF(re2.validUntil, NOW()) = " +
                deltaDays +
                ")",
                RateEvent.class
            )
            .getResultList();
    }
}
