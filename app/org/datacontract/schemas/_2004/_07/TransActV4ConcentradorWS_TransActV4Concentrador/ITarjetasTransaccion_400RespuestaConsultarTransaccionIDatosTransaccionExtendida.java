/**
 * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package org.datacontract.schemas._2004._07.TransActV4ConcentradorWS_TransActV4Concentrador;

public class ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida
        implements java.io.Serializable {
    private java.lang.String cuentaNro;

    private java.lang.String decretoLeyAdqId;

    private int decretoLeyId;

    private java.lang.String decretoLeyNom;

    private java.lang.String decretoLeyVoucher;

    private java.lang.String emisorNombre;

    private java.lang.String empresaNombre;

    private java.lang.String empresaRUT;

    private java.lang.String emvAppId;

    private java.lang.String emvAppName;

    private double facturaMonto;

    private double facturaMontoGravado;

    private double facturaMontoGravadoTrn;

    private double facturaMontoIVA;

    private double facturaMontoIVATrn;

    private double facturaNro;

    private boolean firmarVoucher;

    private java.lang.String merchantID;

    private int planId;

    private java.lang.String planNombre;

    private int planNroPlan;

    private int planNroTipoPlan;

    private java.lang.String sucursalDireccion;

    private java.lang.String sucursalNombre;

    private java.lang.String tarjetaDocIdentidad;

    private java.lang.String tarjetaMedio;

    private java.lang.String tarjetaNombre;

    private java.lang.String tarjetaTitular;

    private java.lang.String tarjetaVencimiento;

    private java.lang.String terminalID;

    private java.lang.String textoAdicional;

    private int tipoCuentaId;

    private java.lang.String tipoCuentaNombre;

    private java.util.Calendar transaccionFechaHora;

    public ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida() {
    }

    public ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida(java.lang.String cuentaNro,
            java.lang.String decretoLeyAdqId, int decretoLeyId, java.lang.String decretoLeyNom,
            java.lang.String decretoLeyVoucher, java.lang.String emisorNombre, java.lang.String empresaNombre,
            java.lang.String empresaRUT, java.lang.String emvAppId, java.lang.String emvAppName, double facturaMonto,
            double facturaMontoGravado, double facturaMontoGravadoTrn, double facturaMontoIVA,
            double facturaMontoIVATrn, double facturaNro, boolean firmarVoucher, java.lang.String merchantID,
            int planId, java.lang.String planNombre, int planNroPlan, int planNroTipoPlan,
            java.lang.String sucursalDireccion, java.lang.String sucursalNombre, java.lang.String tarjetaDocIdentidad,
            java.lang.String tarjetaMedio, java.lang.String tarjetaNombre, java.lang.String tarjetaTitular,
            java.lang.String tarjetaVencimiento, java.lang.String terminalID, java.lang.String textoAdicional,
            int tipoCuentaId, java.lang.String tipoCuentaNombre, java.util.Calendar transaccionFechaHora) {
        this.cuentaNro = cuentaNro;
        this.decretoLeyAdqId = decretoLeyAdqId;
        this.decretoLeyId = decretoLeyId;
        this.decretoLeyNom = decretoLeyNom;
        this.decretoLeyVoucher = decretoLeyVoucher;
        this.emisorNombre = emisorNombre;
        this.empresaNombre = empresaNombre;
        this.empresaRUT = empresaRUT;
        this.emvAppId = emvAppId;
        this.emvAppName = emvAppName;
        this.facturaMonto = facturaMonto;
        this.facturaMontoGravado = facturaMontoGravado;
        this.facturaMontoGravadoTrn = facturaMontoGravadoTrn;
        this.facturaMontoIVA = facturaMontoIVA;
        this.facturaMontoIVATrn = facturaMontoIVATrn;
        this.facturaNro = facturaNro;
        this.firmarVoucher = firmarVoucher;
        this.merchantID = merchantID;
        this.planId = planId;
        this.planNombre = planNombre;
        this.planNroPlan = planNroPlan;
        this.planNroTipoPlan = planNroTipoPlan;
        this.sucursalDireccion = sucursalDireccion;
        this.sucursalNombre = sucursalNombre;
        this.tarjetaDocIdentidad = tarjetaDocIdentidad;
        this.tarjetaMedio = tarjetaMedio;
        this.tarjetaNombre = tarjetaNombre;
        this.tarjetaTitular = tarjetaTitular;
        this.tarjetaVencimiento = tarjetaVencimiento;
        this.terminalID = terminalID;
        this.textoAdicional = textoAdicional;
        this.tipoCuentaId = tipoCuentaId;
        this.tipoCuentaNombre = tipoCuentaNombre;
        this.transaccionFechaHora = transaccionFechaHora;
    }

    /**
     * Gets the cuentaNro value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return cuentaNro
     */
    public java.lang.String getCuentaNro() {
        return cuentaNro;
    }

    /**
     * Sets the cuentaNro value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param cuentaNro
     */
    public void setCuentaNro(java.lang.String cuentaNro) {
        this.cuentaNro = cuentaNro;
    }

    /**
     * Gets the decretoLeyAdqId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return decretoLeyAdqId
     */
    public java.lang.String getDecretoLeyAdqId() {
        return decretoLeyAdqId;
    }

    /**
     * Sets the decretoLeyAdqId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param decretoLeyAdqId
     */
    public void setDecretoLeyAdqId(java.lang.String decretoLeyAdqId) {
        this.decretoLeyAdqId = decretoLeyAdqId;
    }

    /**
     * Gets the decretoLeyId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return decretoLeyId
     */
    public int getDecretoLeyId() {
        return decretoLeyId;
    }

    /**
     * Sets the decretoLeyId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param decretoLeyId
     */
    public void setDecretoLeyId(int decretoLeyId) {
        this.decretoLeyId = decretoLeyId;
    }

    /**
     * Gets the decretoLeyNom value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return decretoLeyNom
     */
    public java.lang.String getDecretoLeyNom() {
        return decretoLeyNom;
    }

    /**
     * Sets the decretoLeyNom value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param decretoLeyNom
     */
    public void setDecretoLeyNom(java.lang.String decretoLeyNom) {
        this.decretoLeyNom = decretoLeyNom;
    }

    /**
     * Gets the decretoLeyVoucher value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return decretoLeyVoucher
     */
    public java.lang.String getDecretoLeyVoucher() {
        return decretoLeyVoucher;
    }

    /**
     * Sets the decretoLeyVoucher value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param decretoLeyVoucher
     */
    public void setDecretoLeyVoucher(java.lang.String decretoLeyVoucher) {
        this.decretoLeyVoucher = decretoLeyVoucher;
    }

    /**
     * Gets the emisorNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return emisorNombre
     */
    public java.lang.String getEmisorNombre() {
        return emisorNombre;
    }

    /**
     * Sets the emisorNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param emisorNombre
     */
    public void setEmisorNombre(java.lang.String emisorNombre) {
        this.emisorNombre = emisorNombre;
    }

    /**
     * Gets the empresaNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return empresaNombre
     */
    public java.lang.String getEmpresaNombre() {
        return empresaNombre;
    }

    /**
     * Sets the empresaNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param empresaNombre
     */
    public void setEmpresaNombre(java.lang.String empresaNombre) {
        this.empresaNombre = empresaNombre;
    }

    /**
     * Gets the empresaRUT value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return empresaRUT
     */
    public java.lang.String getEmpresaRUT() {
        return empresaRUT;
    }

    /**
     * Sets the empresaRUT value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param empresaRUT
     */
    public void setEmpresaRUT(java.lang.String empresaRUT) {
        this.empresaRUT = empresaRUT;
    }

    /**
     * Gets the emvAppId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return emvAppId
     */
    public java.lang.String getEmvAppId() {
        return emvAppId;
    }

    /**
     * Sets the emvAppId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param emvAppId
     */
    public void setEmvAppId(java.lang.String emvAppId) {
        this.emvAppId = emvAppId;
    }

    /**
     * Gets the emvAppName value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return emvAppName
     */
    public java.lang.String getEmvAppName() {
        return emvAppName;
    }

    /**
     * Sets the emvAppName value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param emvAppName
     */
    public void setEmvAppName(java.lang.String emvAppName) {
        this.emvAppName = emvAppName;
    }

    /**
     * Gets the facturaMonto value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return facturaMonto
     */
    public double getFacturaMonto() {
        return facturaMonto;
    }

    /**
     * Sets the facturaMonto value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param facturaMonto
     */
    public void setFacturaMonto(double facturaMonto) {
        this.facturaMonto = facturaMonto;
    }

    /**
     * Gets the facturaMontoGravado value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return facturaMontoGravado
     */
    public double getFacturaMontoGravado() {
        return facturaMontoGravado;
    }

    /**
     * Sets the facturaMontoGravado value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param facturaMontoGravado
     */
    public void setFacturaMontoGravado(double facturaMontoGravado) {
        this.facturaMontoGravado = facturaMontoGravado;
    }

    /**
     * Gets the facturaMontoGravadoTrn value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return facturaMontoGravadoTrn
     */
    public double getFacturaMontoGravadoTrn() {
        return facturaMontoGravadoTrn;
    }

    /**
     * Sets the facturaMontoGravadoTrn value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param facturaMontoGravadoTrn
     */
    public void setFacturaMontoGravadoTrn(double facturaMontoGravadoTrn) {
        this.facturaMontoGravadoTrn = facturaMontoGravadoTrn;
    }

    /**
     * Gets the facturaMontoIVA value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return facturaMontoIVA
     */
    public double getFacturaMontoIVA() {
        return facturaMontoIVA;
    }

    /**
     * Sets the facturaMontoIVA value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param facturaMontoIVA
     */
    public void setFacturaMontoIVA(double facturaMontoIVA) {
        this.facturaMontoIVA = facturaMontoIVA;
    }

    /**
     * Gets the facturaMontoIVATrn value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return facturaMontoIVATrn
     */
    public double getFacturaMontoIVATrn() {
        return facturaMontoIVATrn;
    }

    /**
     * Sets the facturaMontoIVATrn value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param facturaMontoIVATrn
     */
    public void setFacturaMontoIVATrn(double facturaMontoIVATrn) {
        this.facturaMontoIVATrn = facturaMontoIVATrn;
    }

    /**
     * Gets the facturaNro value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return facturaNro
     */
    public double getFacturaNro() {
        return facturaNro;
    }

    /**
     * Sets the facturaNro value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param facturaNro
     */
    public void setFacturaNro(double facturaNro) {
        this.facturaNro = facturaNro;
    }

    /**
     * Gets the firmarVoucher value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return firmarVoucher
     */
    public boolean isFirmarVoucher() {
        return firmarVoucher;
    }

    /**
     * Sets the firmarVoucher value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param firmarVoucher
     */
    public void setFirmarVoucher(boolean firmarVoucher) {
        this.firmarVoucher = firmarVoucher;
    }

    /**
     * Gets the merchantID value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return merchantID
     */
    public java.lang.String getMerchantID() {
        return merchantID;
    }

    /**
     * Sets the merchantID value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param merchantID
     */
    public void setMerchantID(java.lang.String merchantID) {
        this.merchantID = merchantID;
    }

    /**
     * Gets the planId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return planId
     */
    public int getPlanId() {
        return planId;
    }

    /**
     * Sets the planId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param planId
     */
    public void setPlanId(int planId) {
        this.planId = planId;
    }

    /**
     * Gets the planNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return planNombre
     */
    public java.lang.String getPlanNombre() {
        return planNombre;
    }

    /**
     * Sets the planNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param planNombre
     */
    public void setPlanNombre(java.lang.String planNombre) {
        this.planNombre = planNombre;
    }

    /**
     * Gets the planNroPlan value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return planNroPlan
     */
    public int getPlanNroPlan() {
        return planNroPlan;
    }

    /**
     * Sets the planNroPlan value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param planNroPlan
     */
    public void setPlanNroPlan(int planNroPlan) {
        this.planNroPlan = planNroPlan;
    }

    /**
     * Gets the planNroTipoPlan value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return planNroTipoPlan
     */
    public int getPlanNroTipoPlan() {
        return planNroTipoPlan;
    }

    /**
     * Sets the planNroTipoPlan value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param planNroTipoPlan
     */
    public void setPlanNroTipoPlan(int planNroTipoPlan) {
        this.planNroTipoPlan = planNroTipoPlan;
    }

    /**
     * Gets the sucursalDireccion value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return sucursalDireccion
     */
    public java.lang.String getSucursalDireccion() {
        return sucursalDireccion;
    }

    /**
     * Sets the sucursalDireccion value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param sucursalDireccion
     */
    public void setSucursalDireccion(java.lang.String sucursalDireccion) {
        this.sucursalDireccion = sucursalDireccion;
    }

    /**
     * Gets the sucursalNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return sucursalNombre
     */
    public java.lang.String getSucursalNombre() {
        return sucursalNombre;
    }

    /**
     * Sets the sucursalNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param sucursalNombre
     */
    public void setSucursalNombre(java.lang.String sucursalNombre) {
        this.sucursalNombre = sucursalNombre;
    }

    /**
     * Gets the tarjetaDocIdentidad value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return tarjetaDocIdentidad
     */
    public java.lang.String getTarjetaDocIdentidad() {
        return tarjetaDocIdentidad;
    }

    /**
     * Sets the tarjetaDocIdentidad value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param tarjetaDocIdentidad
     */
    public void setTarjetaDocIdentidad(java.lang.String tarjetaDocIdentidad) {
        this.tarjetaDocIdentidad = tarjetaDocIdentidad;
    }

    /**
     * Gets the tarjetaMedio value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return tarjetaMedio
     */
    public java.lang.String getTarjetaMedio() {
        return tarjetaMedio;
    }

    /**
     * Sets the tarjetaMedio value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param tarjetaMedio
     */
    public void setTarjetaMedio(java.lang.String tarjetaMedio) {
        this.tarjetaMedio = tarjetaMedio;
    }

    /**
     * Gets the tarjetaNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return tarjetaNombre
     */
    public java.lang.String getTarjetaNombre() {
        return tarjetaNombre;
    }

    /**
     * Sets the tarjetaNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param tarjetaNombre
     */
    public void setTarjetaNombre(java.lang.String tarjetaNombre) {
        this.tarjetaNombre = tarjetaNombre;
    }

    /**
     * Gets the tarjetaTitular value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return tarjetaTitular
     */
    public java.lang.String getTarjetaTitular() {
        return tarjetaTitular;
    }

    /**
     * Sets the tarjetaTitular value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param tarjetaTitular
     */
    public void setTarjetaTitular(java.lang.String tarjetaTitular) {
        this.tarjetaTitular = tarjetaTitular;
    }

    /**
     * Gets the tarjetaVencimiento value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return tarjetaVencimiento
     */
    public java.lang.String getTarjetaVencimiento() {
        return tarjetaVencimiento;
    }

    /**
     * Sets the tarjetaVencimiento value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param tarjetaVencimiento
     */
    public void setTarjetaVencimiento(java.lang.String tarjetaVencimiento) {
        this.tarjetaVencimiento = tarjetaVencimiento;
    }

    /**
     * Gets the terminalID value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return terminalID
     */
    public java.lang.String getTerminalID() {
        return terminalID;
    }

    /**
     * Sets the terminalID value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param terminalID
     */
    public void setTerminalID(java.lang.String terminalID) {
        this.terminalID = terminalID;
    }

    /**
     * Gets the textoAdicional value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return textoAdicional
     */
    public java.lang.String getTextoAdicional() {
        return textoAdicional;
    }

    /**
     * Sets the textoAdicional value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param textoAdicional
     */
    public void setTextoAdicional(java.lang.String textoAdicional) {
        this.textoAdicional = textoAdicional;
    }

    /**
     * Gets the tipoCuentaId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return tipoCuentaId
     */
    public int getTipoCuentaId() {
        return tipoCuentaId;
    }

    /**
     * Sets the tipoCuentaId value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param tipoCuentaId
     */
    public void setTipoCuentaId(int tipoCuentaId) {
        this.tipoCuentaId = tipoCuentaId;
    }

    /**
     * Gets the tipoCuentaNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return tipoCuentaNombre
     */
    public java.lang.String getTipoCuentaNombre() {
        return tipoCuentaNombre;
    }

    /**
     * Sets the tipoCuentaNombre value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param tipoCuentaNombre
     */
    public void setTipoCuentaNombre(java.lang.String tipoCuentaNombre) {
        this.tipoCuentaNombre = tipoCuentaNombre;
    }

    /**
     * Gets the transaccionFechaHora value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @return transaccionFechaHora
     */
    public java.util.Calendar getTransaccionFechaHora() {
        return transaccionFechaHora;
    }

    /**
     * Sets the transaccionFechaHora value for this
     * ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.
     * 
     * @param transaccionFechaHora
     */
    public void setTransaccionFechaHora(java.util.Calendar transaccionFechaHora) {
        this.transaccionFechaHora = transaccionFechaHora;
    }

    private java.lang.Object __equalsCalc = null;

    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida))
            return false;
        ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida other = (ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida) obj;
        if (obj == null)
            return false;
        if (this == obj)
            return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true
                && ((this.cuentaNro == null && other.getCuentaNro() == null)
                        || (this.cuentaNro != null && this.cuentaNro.equals(other.getCuentaNro())))
                && ((this.decretoLeyAdqId == null && other.getDecretoLeyAdqId() == null)
                        || (this.decretoLeyAdqId != null && this.decretoLeyAdqId.equals(other.getDecretoLeyAdqId())))
                && this.decretoLeyId == other.getDecretoLeyId()
                && ((this.decretoLeyNom == null && other.getDecretoLeyNom() == null)
                        || (this.decretoLeyNom != null && this.decretoLeyNom.equals(other.getDecretoLeyNom())))
                && ((this.decretoLeyVoucher == null && other.getDecretoLeyVoucher() == null)
                        || (this.decretoLeyVoucher != null
                                && this.decretoLeyVoucher.equals(other.getDecretoLeyVoucher())))
                && ((this.emisorNombre == null && other.getEmisorNombre() == null)
                        || (this.emisorNombre != null && this.emisorNombre.equals(other.getEmisorNombre())))
                && ((this.empresaNombre == null && other.getEmpresaNombre() == null)
                        || (this.empresaNombre != null && this.empresaNombre.equals(other.getEmpresaNombre())))
                && ((this.empresaRUT == null && other.getEmpresaRUT() == null)
                        || (this.empresaRUT != null && this.empresaRUT.equals(other.getEmpresaRUT())))
                && ((this.emvAppId == null && other.getEmvAppId() == null)
                        || (this.emvAppId != null && this.emvAppId.equals(other.getEmvAppId())))
                && ((this.emvAppName == null && other.getEmvAppName() == null)
                        || (this.emvAppName != null && this.emvAppName.equals(other.getEmvAppName())))
                && this.facturaMonto == other.getFacturaMonto()
                && this.facturaMontoGravado == other.getFacturaMontoGravado()
                && this.facturaMontoGravadoTrn == other.getFacturaMontoGravadoTrn()
                && this.facturaMontoIVA == other.getFacturaMontoIVA()
                && this.facturaMontoIVATrn == other.getFacturaMontoIVATrn() && this.facturaNro == other.getFacturaNro()
                && this.firmarVoucher == other.isFirmarVoucher()
                && ((this.merchantID == null && other.getMerchantID() == null)
                        || (this.merchantID != null && this.merchantID.equals(other.getMerchantID())))
                && this.planId == other.getPlanId()
                && ((this.planNombre == null && other.getPlanNombre() == null)
                        || (this.planNombre != null && this.planNombre.equals(other.getPlanNombre())))
                && this.planNroPlan == other.getPlanNroPlan() && this.planNroTipoPlan == other.getPlanNroTipoPlan()
                && ((this.sucursalDireccion == null && other.getSucursalDireccion() == null)
                        || (this.sucursalDireccion != null
                                && this.sucursalDireccion.equals(other.getSucursalDireccion())))
                && ((this.sucursalNombre == null && other.getSucursalNombre() == null)
                        || (this.sucursalNombre != null && this.sucursalNombre.equals(other.getSucursalNombre())))
                && ((this.tarjetaDocIdentidad == null && other.getTarjetaDocIdentidad() == null)
                        || (this.tarjetaDocIdentidad != null
                                && this.tarjetaDocIdentidad.equals(other.getTarjetaDocIdentidad())))
                && ((this.tarjetaMedio == null && other.getTarjetaMedio() == null)
                        || (this.tarjetaMedio != null && this.tarjetaMedio.equals(other.getTarjetaMedio())))
                && ((this.tarjetaNombre == null && other.getTarjetaNombre() == null)
                        || (this.tarjetaNombre != null && this.tarjetaNombre.equals(other.getTarjetaNombre())))
                && ((this.tarjetaTitular == null && other.getTarjetaTitular() == null)
                        || (this.tarjetaTitular != null && this.tarjetaTitular.equals(other.getTarjetaTitular())))
                && ((this.tarjetaVencimiento == null && other.getTarjetaVencimiento() == null)
                        || (this.tarjetaVencimiento != null
                                && this.tarjetaVencimiento.equals(other.getTarjetaVencimiento())))
                && ((this.terminalID == null && other.getTerminalID() == null)
                        || (this.terminalID != null && this.terminalID.equals(other.getTerminalID())))
                && ((this.textoAdicional == null && other.getTextoAdicional() == null)
                        || (this.textoAdicional != null && this.textoAdicional.equals(other.getTextoAdicional())))
                && this.tipoCuentaId == other.getTipoCuentaId()
                && ((this.tipoCuentaNombre == null && other.getTipoCuentaNombre() == null)
                        || (this.tipoCuentaNombre != null && this.tipoCuentaNombre.equals(other.getTipoCuentaNombre())))
                && ((this.transaccionFechaHora == null && other.getTransaccionFechaHora() == null)
                        || (this.transaccionFechaHora != null
                                && this.transaccionFechaHora.equals(other.getTransaccionFechaHora())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;

    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getCuentaNro() != null) {
            _hashCode += getCuentaNro().hashCode();
        }
        if (getDecretoLeyAdqId() != null) {
            _hashCode += getDecretoLeyAdqId().hashCode();
        }
        _hashCode += getDecretoLeyId();
        if (getDecretoLeyNom() != null) {
            _hashCode += getDecretoLeyNom().hashCode();
        }
        if (getDecretoLeyVoucher() != null) {
            _hashCode += getDecretoLeyVoucher().hashCode();
        }
        if (getEmisorNombre() != null) {
            _hashCode += getEmisorNombre().hashCode();
        }
        if (getEmpresaNombre() != null) {
            _hashCode += getEmpresaNombre().hashCode();
        }
        if (getEmpresaRUT() != null) {
            _hashCode += getEmpresaRUT().hashCode();
        }
        if (getEmvAppId() != null) {
            _hashCode += getEmvAppId().hashCode();
        }
        if (getEmvAppName() != null) {
            _hashCode += getEmvAppName().hashCode();
        }
        _hashCode += new Double(getFacturaMonto()).hashCode();
        _hashCode += new Double(getFacturaMontoGravado()).hashCode();
        _hashCode += new Double(getFacturaMontoGravadoTrn()).hashCode();
        _hashCode += new Double(getFacturaMontoIVA()).hashCode();
        _hashCode += new Double(getFacturaMontoIVATrn()).hashCode();
        _hashCode += new Double(getFacturaNro()).hashCode();
        _hashCode += (isFirmarVoucher() ? Boolean.TRUE : Boolean.FALSE).hashCode();
        if (getMerchantID() != null) {
            _hashCode += getMerchantID().hashCode();
        }
        _hashCode += getPlanId();
        if (getPlanNombre() != null) {
            _hashCode += getPlanNombre().hashCode();
        }
        _hashCode += getPlanNroPlan();
        _hashCode += getPlanNroTipoPlan();
        if (getSucursalDireccion() != null) {
            _hashCode += getSucursalDireccion().hashCode();
        }
        if (getSucursalNombre() != null) {
            _hashCode += getSucursalNombre().hashCode();
        }
        if (getTarjetaDocIdentidad() != null) {
            _hashCode += getTarjetaDocIdentidad().hashCode();
        }
        if (getTarjetaMedio() != null) {
            _hashCode += getTarjetaMedio().hashCode();
        }
        if (getTarjetaNombre() != null) {
            _hashCode += getTarjetaNombre().hashCode();
        }
        if (getTarjetaTitular() != null) {
            _hashCode += getTarjetaTitular().hashCode();
        }
        if (getTarjetaVencimiento() != null) {
            _hashCode += getTarjetaVencimiento().hashCode();
        }
        if (getTerminalID() != null) {
            _hashCode += getTerminalID().hashCode();
        }
        if (getTextoAdicional() != null) {
            _hashCode += getTextoAdicional().hashCode();
        }
        _hashCode += getTipoCuentaId();
        if (getTipoCuentaNombre() != null) {
            _hashCode += getTipoCuentaNombre().hashCode();
        }
        if (getTransaccionFechaHora() != null) {
            _hashCode += getTransaccionFechaHora().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc = new org.apache.axis.description.TypeDesc(
            ITarjetasTransaccion_400RespuestaConsultarTransaccionIDatosTransaccionExtendida.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "ITarjetasTransaccion_400.RespuestaConsultarTransaccion.IDatosTransaccionExtendida"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("cuentaNro");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "CuentaNro"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("decretoLeyAdqId");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "DecretoLeyAdqId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("decretoLeyId");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "DecretoLeyId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("decretoLeyNom");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "DecretoLeyNom"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("decretoLeyVoucher");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "DecretoLeyVoucher"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("emisorNombre");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "EmisorNombre"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("empresaNombre");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "EmpresaNombre"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("empresaRUT");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "EmpresaRUT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("emvAppId");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "EmvAppId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("emvAppName");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "EmvAppName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMonto");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMonto"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMontoGravado");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMontoGravado"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMontoGravadoTrn");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMontoGravadoTrn"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMontoIVA");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMontoIVA"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaMontoIVATrn");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaMontoIVATrn"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("facturaNro");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FacturaNro"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("firmarVoucher");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "FirmarVoucher"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("merchantID");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "MerchantID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("planId");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador", "PlanId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("planNombre");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "PlanNombre"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("planNroPlan");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "PlanNroPlan"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("planNroTipoPlan");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "PlanNroTipoPlan"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sucursalDireccion");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "SucursalDireccion"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("sucursalNombre");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "SucursalNombre"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaDocIdentidad");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaDocIdentidad"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaMedio");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaMedio"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaNombre");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaNombre"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaTitular");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaTitular"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tarjetaVencimiento");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TarjetaVencimiento"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("terminalID");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TerminalID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("textoAdicional");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TextoAdicional"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tipoCuentaId");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TipoCuentaId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("tipoCuentaNombre");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TipoCuentaNombre"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("transaccionFechaHora");
        elemField.setXmlName(new javax.xml.namespace.QName(
                "http://schemas.datacontract.org/2004/07/TransActV4ConcentradorWS.TransActV4Concentrador",
                "TransaccionFechaHora"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(java.lang.String mechType,
            java.lang.Class _javaType, javax.xml.namespace.QName _xmlType) {
        return new org.apache.axis.encoding.ser.BeanSerializer(_javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(java.lang.String mechType,
            java.lang.Class _javaType, javax.xml.namespace.QName _xmlType) {
        return new org.apache.axis.encoding.ser.BeanDeserializer(_javaType, _xmlType, typeDesc);
    }

}
