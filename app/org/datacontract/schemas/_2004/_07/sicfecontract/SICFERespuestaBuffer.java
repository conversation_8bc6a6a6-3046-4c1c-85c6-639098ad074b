
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SICFERespuestaBuffer complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuestaBuffer">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="Buffer" type="{http://www.w3.org/2001/XMLSchema}base64Binary" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuestaBuffer", propOrder = { "buffer" })
public class SICFERespuestaBuffer extends SICFERespuesta {

    @XmlElementRef(name = "Buffer", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<byte[]> buffer;

    /**
     * Gets the value of the buffer property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link byte[]}{@code >}
     * 
     */
    public JAXBElement<byte[]> getBuffer() {
        return buffer;
    }

    /**
     * Sets the value of the buffer property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link byte[]}{@code >}
     * 
     */
    public void setBuffer(JAXBElement<byte[]> value) {
        this.buffer = value;
    }

}
