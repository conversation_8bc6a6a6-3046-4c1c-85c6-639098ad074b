
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SICFERespuestaEmisorReceptorElectronico complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuestaEmisorReceptorElectronico">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="Electronico" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="EmailIntercambio" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RazonSocial" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuestaEmisorReceptorElectronico", propOrder = { "electronico", "emailIntercambio",
        "razonSocial" })
public class SICFERespuestaEmisorReceptorElectronico extends SICFERespuesta {

    @XmlElement(name = "Electronico")
    protected Boolean electronico;
    @XmlElementRef(name = "EmailIntercambio", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> emailIntercambio;
    @XmlElementRef(name = "RazonSocial", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> razonSocial;

    /**
     * Gets the value of the electronico property.
     * 
     * @return possible object is {@link Boolean }
     * 
     */
    public Boolean isElectronico() {
        return electronico;
    }

    /**
     * Sets the value of the electronico property.
     * 
     * @param value allowed object is {@link Boolean }
     * 
     */
    public void setElectronico(Boolean value) {
        this.electronico = value;
    }

    /**
     * Gets the value of the emailIntercambio property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getEmailIntercambio() {
        return emailIntercambio;
    }

    /**
     * Sets the value of the emailIntercambio property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setEmailIntercambio(JAXBElement<String> value) {
        this.emailIntercambio = value;
    }

    /**
     * Gets the value of the razonSocial property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getRazonSocial() {
        return razonSocial;
    }

    /**
     * Sets the value of the razonSocial property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setRazonSocial(JAXBElement<String> value) {
        this.razonSocial = value;
    }

}
