
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SICFERespuestaCertificados complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuestaCertificados">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="Certificados" type="{http://schemas.datacontract.org/2004/07/SICFEContract}ArrayOfCertificado" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuestaCertificados", propOrder = { "certificados" })
public class SICFERespuestaCertificados extends SICFERespuesta {

    @XmlElementRef(name = "Certificados", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<ArrayOfCertificado> certificados;

    /**
     * Gets the value of the certificados property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link ArrayOfCertificado }{@code >}
     * 
     */
    public JAXBElement<ArrayOfCertificado> getCertificados() {
        return certificados;
    }

    /**
     * Sets the value of the certificados property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link ArrayOfCertificado }{@code >}
     * 
     */
    public void setCertificados(JAXBElement<ArrayOfCertificado> value) {
        this.certificados = value;
    }

}
