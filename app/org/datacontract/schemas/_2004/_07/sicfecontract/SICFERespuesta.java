
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for SICFERespuesta complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SICFERespuesta">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="Codigo" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="Descripcion" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SICFERespuesta", propOrder = { "codigo", "descripcion" })
@XmlSeeAlso({ SICFERespuestaObtenerXML.class, SICFERespuestaReimpresion.class, RespuestaObtenerRecursosImpresion.class,
        SICFERespuestaEnvioCFE.class, RespuestaObtenerCFEsRecibidosExtendido.class, SICFERespuestaObtenerCFE.class,
        SICFERespuestaBuffer.class, ReservarNro.class, RespuestaObtenerCAE.class, SICFERespuestaPing.class,
        SICFERespuestaEmisorReceptorElectronico.class, SICFERespuestaObtenerClientesElectronicos.class,
        ObtenerTemplateImpresionRespuesta.class, SICFERespuestaObtenerProveedoresElectronicos.class,
        SICFERespuestaCertificados.class, SICFERespuestaDatosEmisorReceptor.class, SicfeRespuestaConsolidar.class,
        RespuestaObtenerCFEsRecibidos.class, SICFERespuestaEstadoCFE.class, SICFERespuestaVersion.class })
public class SICFERespuesta {

    @XmlElement(name = "Codigo")
    protected Integer codigo;
    @XmlElementRef(name = "Descripcion", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<String> descripcion;

    /**
     * Gets the value of the codigo property.
     * 
     * @return possible object is {@link Integer }
     * 
     */
    public Integer getCodigo() {
        return codigo;
    }

    /**
     * Sets the value of the codigo property.
     * 
     * @param value allowed object is {@link Integer }
     * 
     */
    public void setCodigo(Integer value) {
        this.codigo = value;
    }

    /**
     * Gets the value of the descripcion property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link String
     *         }{@code >}
     * 
     */
    public JAXBElement<String> getDescripcion() {
        return descripcion;
    }

    /**
     * Sets the value of the descripcion property.
     * 
     * @param value allowed object is {@link JAXBElement }{@code <}{@link String
     *              }{@code >}
     * 
     */
    public void setDescripcion(JAXBElement<String> value) {
        this.descripcion = value;
    }

}
