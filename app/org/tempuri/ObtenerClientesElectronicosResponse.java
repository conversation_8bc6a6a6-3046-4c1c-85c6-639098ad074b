
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaObtenerClientesElectronicos;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ObtenerClientesElectronicosResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuestaObtenerClientesElectronicos" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "obtenerClientesElectronicosResult" })
@XmlRootElement(name = "ObtenerClientesElectronicosResponse")
public class ObtenerClientesElectronicosResponse {

    @XmlElementRef(name = "ObtenerClientesElectronicosResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<SICFERespuestaObtenerClientesElectronicos> obtenerClientesElectronicosResult;

    /**
     * Gets the value of the obtenerClientesElectronicosResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link SICFERespuestaObtenerClientesElectronicos }{@code >}
     * 
     */
    public JAXBElement<SICFERespuestaObtenerClientesElectronicos> getObtenerClientesElectronicosResult() {
        return obtenerClientesElectronicosResult;
    }

    /**
     * Sets the value of the obtenerClientesElectronicosResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link SICFERespuestaObtenerClientesElectronicos
     *              }{@code >}
     * 
     */
    public void setObtenerClientesElectronicosResult(JAXBElement<SICFERespuestaObtenerClientesElectronicos> value) {
        this.obtenerClientesElectronicosResult = value;
    }

}
