
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuesta;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ActualizarReceptoresNoElectronicosResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "actualizarReceptoresNoElectronicosResult" })
@XmlRootElement(name = "ActualizarReceptoresNoElectronicosResponse")
public class ActualizarReceptoresNoElectronicosResponse {

    @XmlElementRef(name = "ActualizarReceptoresNoElectronicosResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<SICFERespuesta> actualizarReceptoresNoElectronicosResult;

    /**
     * Gets the value of the actualizarReceptoresNoElectronicosResult property.
     * 
     * @return possible object is {@link JAXBElement }{@code <}{@link SICFERespuesta
     *         }{@code >}
     * 
     */
    public JAXBElement<SICFERespuesta> getActualizarReceptoresNoElectronicosResult() {
        return actualizarReceptoresNoElectronicosResult;
    }

    /**
     * Sets the value of the actualizarReceptoresNoElectronicosResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link SICFERespuesta }{@code >}
     * 
     */
    public void setActualizarReceptoresNoElectronicosResult(JAXBElement<SICFERespuesta> value) {
        this.actualizarReceptoresNoElectronicosResult = value;
    }

}
