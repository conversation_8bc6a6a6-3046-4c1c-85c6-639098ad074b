package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import utils.WebReportWrapper.WebReportItemWrapper;

public class WebReportItemWrapperSerializer {

    public static JSONObject itemListToJson(List<WebReportItemWrapper> uses, int level)
        throws APIException {
        JSONObject usesJson = new JSONObject();

        JSONArray usesListJson = itemListToJsonList(uses, level);

        try {
            usesJson.put("uses", usesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing uses report : " + e.getMessage());
        }
        return usesJson;
    }

    public static JSONArray itemListToJsonList(List<WebReportItemWrapper> uses, int level)
        throws APIException {
        JSONArray usesListJson = new JSONArray();
        for (WebReportItemWrapper use : uses) {
            usesListJson.put(useToJson(use, level));
        }

        return usesListJson;
    }

    public static JSONObject useToJson(WebReportItemWrapper use, int level) throws APIException {
        JSONObject useJson = new JSONObject();

        try {
            if (level >= 0) {
                useJson.put("timestamp", use.getTimestamp());
                useJson.put("type", use.getType());
                useJson.put("amount", use.getPrice());
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing use : " + e.getMessage());
        }

        return useJson;
    }
}
