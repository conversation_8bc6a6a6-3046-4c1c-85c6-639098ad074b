package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.BrandingItemRequest;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class BrandingItemRequestsSerializer {

    public static JSONObject itemListToJson(List<BrandingItemRequest> items, int level)
        throws APIException {
        JSONObject itemsJson = new JSONObject();

        JSONArray itemsListJson = new JSONArray();
        for (BrandingItemRequest b : items) {
            itemsListJson.put(itemToJson(b, level));
        }

        try {
            itemsJson.put("requests", itemsListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing Branding Item list : " + e.getMessage());
        }
        return itemsJson;
    }

    public static JSONObject itemToJson(BrandingItemRequest b, int level) throws APIException {
        JSONObject itemJson = new JSONObject();

        try {
            itemJson.put("id", b.getId());
            itemJson.put("creation_date", b.getCreationDate().getTime());
            itemJson.put("delivered", b.isDelivered());
            itemJson.put(
                "delivery_date",
                b.getDeliveryDate() != null ? b.getDeliveryDate().getTime() : JSONObject.NULL
            );
            itemJson.put("item_id", b.getBrandingItem().getId());
            itemJson.put("item_name", b.getBrandingItem().getName());
            itemJson.put("transaction_id", b.getTransaction().getId());
            itemJson.put("address", b.getTransaction().getAddress());
            itemJson.put("quantity", b.getTransaction().getQuantity());
            itemJson.put("contact_email", b.getTransaction().getEmail());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing building : " + e.getMessage());
        }

        return itemJson;
    }
}
