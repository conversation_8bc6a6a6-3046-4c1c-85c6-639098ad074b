package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.Machine;
import models.Replacement;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class ReplacementSerializer {

    public static JSONObject replacementListToJson(List<Replacement> replacements, int level)
        throws APIException {
        JSONObject replacementsJson = new JSONObject();

        JSONArray replacementsListJson = new JSONArray();
        for (Replacement r : replacements) {
            replacementsListJson.put(replacementToJson(r, level));
        }

        try {
            replacementsJson.put("replacements", replacementsListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing replacement list : " + e.getMessage());
        }
        return replacementsJson;
    }

    public static JSONObject replacementToJson(Replacement r, int level) throws APIException {
        JSONObject replacementJson = PartsSerializer.partToJson(r, level);

        try {
            replacementJson.put("english_description", r.getEnglishDescription());
            replacementJson.put("unit_price", r.getUnitPrice());
            replacementJson.put("uy_price", r.getUyPrice());
            replacementJson.put("minimum_stock", r.getMinimumStock());
            replacementJson.put("anual_consumption", r.getAnualConsumption());
            replacementJson.put("request_point", r.getRequestPoint());
            replacementJson.put("quantity", r.getQuantity());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing replacement : " + e.getMessage());
        }

        return replacementJson;
    }
}
