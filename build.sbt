name := """lm_app"""

version := "1.0-SNAPSHOT"

// JoCoCo settings
// https://github.com/sbt/sbt-jacoco/issues/88#issuecomment-333192827
lazy val jacoco = Seq(
  jacocoReportSettings in Test := JacocoReportSettings()
    .withFormats(JacocoReportFormats.ScalaHTML, JacocoReportFormats.XML),
  jacocoExcludes in Test := Seq(
    "controllers.Reverse*",
    "controllers.javascript.*",
    "jooq.*",
    "org.*",
    "cotiza.*",
    "Module",
    "router.Routes*",
    "*.routes*"
  )
)

lazy val root = (project in file("."))
  .enablePlugins(PlayJava)
  .settings(
    // Disable NPM node modules for testing - https://stackoverflow.com/a/51300673/8371888
    JsEngineKeys.npmNodeModules in Assets := Nil,
    JsEngineKeys.npmNodeModules in TestAssets := Nil
  )
  .settings(jacoco)


scalaVersion := "2.11.6"

val env = sys.props.get("env").getOrElse("prod")

// Set 'repo' folder as local dependencies repository
resolvers += Resolver.file("Local repo", file("./repo"))(Resolver.ivyStylePatterns)

libraryDependencies ++= Seq(
  javaJdbc,
  javaWs,
  javaJpa,
  "org.json" % "json" % "20090211",
  "org.hibernate" % "hibernate-entitymanager" % "4.3.9.Final",
  "play4jpa" %% "play4jpa" % "1.0-SNAPSHOT",
  "mysql" % "mysql-connector-java" % "8.0.26",
  "org.apache.poi" % "poi" % "5.4.1",
  "org.apache.poi" % "poi-ooxml" % "5.4.1",
  "com.typesafe.play" %% "play-mailer" % "5.0.0-M1",
  "org.apache.kafka" % "kafka-clients" % "2.1.0",
  "org.eclipse.paho" % "org.eclipse.paho.client.mqttv3" % "1.2.2",
  "com.mercadopago" % "dx-java" % "1.0.33",
  "org.apache.axis" % "axis" % "1.4",
  "javax.xml" % "jaxrpc-api" % "1.1",
  "commons-discovery" % "commons-discovery" % "0.5",
  "wsdl4j" % "wsdl4j" % "1.6.2",
  "ai.djl.mxnet" % "mxnet-model-zoo" % "0.10.0"
)

libraryDependencies += "com.typesafe.play" %% "play-mailer" % "2.4.1"
libraryDependencies += "com.typesafe.play" %% "play-mailer-guice" % "6.0.1"
libraryDependencies += "ai.djl.mxnet" % "mxnet-model-zoo" % "0.10.0"

// Testing libs
libraryDependencies += "com.github.javafaker" % "javafaker" % "1.0.2" % Test
libraryDependencies += "junit" % "junit" % "4.13" % Test
libraryDependencies += "org.easymock" % "easymock" % "4.3" % Test
libraryDependencies += "org.powermock" % "powermock-module-junit4" % "2.0.9" % Test
libraryDependencies += "org.powermock" % "powermock-api-easymock" % "2.0.9" % Test
libraryDependencies += "org.powermock" % "powermock-core" % "2.0.9" % Test

libraryDependencies += filters

// play-redis
// https://mvnrepository.com/artifact/redis.clients/jedis// https://mvnrepository.com/artifact/redis.clients/jedis
libraryDependencies += "redis.clients" % "jedis" % "5.2.0"

// Play provides two styles of routers, one expects its actions to be injected, the
// other, legacy style, accesses its actions statically.
routesGenerator := InjectedRoutesGenerator

// Compile the project before generating Eclipse files, so that generated .scala or .class files for views and routes are present
EclipseKeys.preTasks := Seq(compile in Compile)

EclipseKeys.projectFlavor := EclipseProjectFlavor.Java // Java project. Don't expect Scala IDE
EclipseKeys.createSrc := EclipseCreateSrc.ValueSet(EclipseCreateSrc.ManagedClasses, EclipseCreateSrc.ManagedResources) // Use .class files instead of generated .scala files for views and routes

sourceDirectories in(Compile, TwirlKeys.compileTemplates) := (unmanagedSourceDirectories in Compile).value

// To deploy JPA application
PlayKeys.externalizeResources := false

sources in(Compile, doc) := Seq.empty
publishArtifact in(Compile, packageDoc) := false

fork in Test := true

// Print the value of sys.props.get("env") when SBT loads
onLoad in Global := {
  val oldOnLoad = (onLoad in Global).value
  oldOnLoad andThen { state =>
    println(s"sbt Environment: ${env}")
    state
  }
}
