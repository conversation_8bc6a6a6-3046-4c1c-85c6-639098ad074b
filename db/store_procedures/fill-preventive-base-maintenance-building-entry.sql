CREATE PROCEDURE FillPreventiveBaseMaintenanceBuildingEntry()
BEGIN
    DECLARE today DATE;
    SET today = CURDATE();
    INSERT INTO preventive_maintenance_building_entry(building_id,
                                                      maintenance_type,
                                                      machine_id,
                                                      maintenance_date,
                                                      technician,
                                                      uses,
                                                      created_at)
    SELECT b.id      AS building_id,
           mpType.mp AS maintenance_type,
           NULL      AS machine_id,
           NULL      AS maintenance_date,
           NULL      AS technician,
           0         AS uses,
           today     AS created_at
    FROM building b
             CROSS JOIN (SELECT "MP100" AS mp
                         UNION
                         SELECT "MP500" AS mp
                         UNION
                         SELECT "MP1200" AS mp) mpType
    WHERE b.is_enabled_for_maintenance IS TRUE;

    REPLACE INTO preventive_maintenance_building_entry(building_id,
                                                       maintenance_type,
                                                       machine_id,
                                                       maintenance_date,
                                                       technician,
                                                       uses,
                                                       created_at)
    SELECT e.building_id,
           e.maintenance_type,
           NULL          AS machine_id,
           mt.timestamp  AS maintenance_date,
           mt.technician AS technician,
           0             AS uses,
           e.created_at
    FROM preventive_maintenance_building_entry e
             LEFT JOIN maintenance mt ON e.building_id = mt.building_id
    WHERE mt.id = (SELECT mt1.id
                   FROM maintenance mt1
                   WHERE e.maintenance_type = mt1.maintenance_type
                     AND e.building_id = mt1.building_id
                   ORDER BY mt1.timestamp DESC
                   LIMIT 1)
      AND e.created_at = today;
END
