DROP PROCEDURE IF EXISTS InsertBuildingSettingsAndAssignFK;

DELIMITER $$
CREATE PROCEDURE InsertBuildingSettingsAndAssignFK()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE building_id INT;

    DECLARE cur CURSOR FOR SELECT id FROM lavomat.building order by id;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    START TRANSACTION;
    OPEN cur;

    read_loop:
    LOOP
        FETCH cur INTO building_id;
        IF done THEN
            LEAVE read_loop;
        END IF;

        INSERT INTO lavomat.building_setting (pre_blocked_uses)
        VALUES (0);

        SET @last_building_setting_id = (SELECT MAX(id) FROM lavomat.building_setting);

        UPDATE lavomat.building b
        SET b.building_setting_id = @last_building_setting_id
        WHERE b.id = building_id;
    END LOOP;

    CLOSE cur;
    COMMIT;
END $$

DELIMITER ;

CALL InsertBuildingSettingsAndAssignFK();
DROP PROCEDURE IF EXISTS InsertBuildingSettingsAndAssignFK;

UPDATE lavomat.part c
SET c.pre_blocked_uses = 0
WHERE c.id > 0;
