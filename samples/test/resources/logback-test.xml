<configuration>

    <conversionRule conversionWord="coloredLevel" converterClass="play.api.Logger$ColoredLevel" />

    <timestamp key="bySecond" datePattern="yyyyMMdd'T'HHmmss"/>

    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/test-${bySecond}.log</file>
        <!-- encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5coloredLevel %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5coloredLevel %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO" additivity="false">
        <appender-ref ref="FILE"/>
        <appender-ref ref="STDOUT"/>
    </root>

    <logger name="application" level="DEBUG" additivity="false">
        <appender-ref ref="FILE"/>
        <appender-ref ref="STDOUT"/>
    </logger>

</configuration>