<aside class="sidebar fixed" ng-controller="SidebarController">
  <div class="brand-logo">
    <div>
      <img src="/assets/img/new-logo.png" alt="Lavomat Logo" width="150" />
    </div>
  </div>
  <div class="user-logged-in">
    <div class="content">
      <div class="user-name">
        {{account.user.name}}
        <span class="text-muted f9">{{account.user.role}}</span>
      </div>
      <div class="user-email">{{account.user.email}}</div>
      <div class="user-actions">
        <a class="m-r-5" href="">settings</a>
        <a href="" ng-click="logout()">logout</a>
      </div>
    </div>
  </div>
  <ul
    class="menu-links"
    ng-cloak
    ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT, roles.TECHNICIAN, roles.SUPERVISOR)"
  >
    <a menu-link href="#/dashboard" icon="md md-blur-on">Dashboard</a>
    <a
      menu-link
      href="#/administrations"
      icon="md md-account-balance"
      ng-if="account.anyRole(roles.MASTER)"
      >Administraciones</a
    >
    <a menu-link href="#/buildings" icon="md md-domain">Edificios</a>
    <a
      menu-link
      href="#/rates"
      icon="md md-attach-money"
      ng-if="account.anyRole(roles.MASTER)"
      >Rates</a
    >
    <!--
                            <a menu-link href="#/closureData-load" icon="md md-sim-card">Carga de Usos</a>
                        -->
    <a
      menu-link
      href="#/users"
      icon="md md-account-child"
      ng-if="account.anyRole(roles.MASTER)"
      >Admin Usuarios</a
    >
    <a
      menu-link
      href="#/parts"
      icon="md md-list"
      ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT, roles.SUPERVISOR)"
      >Inventario</a
    >
    <a
      menu-link
      href="#/branding"
      icon="md md-plus-one"
      ng-if="account.anyRole(roles.MASTER)"
      >Accesorios</a
    >
    <li
      menu-toggle
      name="Mantenimiento"
      icon="md md-settings"
      ng-if="account.anyRole(roles.MASTER, roles.SUPERVISOR)"
    >
      <a menu-link href="#/maintenance/preventive" icon="md md-local-parking"
        >Preventivo</a
      >
      <a menu-link href="#/maintenance/corrective" icon="md md-beenhere"
        >Correctivo</a
      >
    </li>
  </ul>

  <ul
    class="menu-links"
    ng-cloak
    ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT)"
  >
    <li menu-toggle name="Facturación" icon="md md-content-paste">
      <a
        menu-link
        href="#/billing/bill-management"
        icon="md md-assignment"
        ng-if="account.anyRole(roles.MASTER)"
        >Gestión de Facturas</a
      >
      <a
        menu-link
        href="#/reports/bill"
        icon="md md-assignment"
        ng-if="account.anyRole(roles.MASTER)"
        >Emitir Facturas</a
      >
      <a
        menu-link
        href="#/auditing/audit-management"
        icon="md md-assignment"
        ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT)"
        >Auditoría de Saldos</a
      >
      <a
        menu-link
        href="#/transactions/transaction-management"
        icon="md md-assignment"
        ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT)"
        >Transacciones</a
      >
    </li>
  </ul>

  <ul
    class="menu-links"
    ng-cloak
    ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT, roles.SUPERVISOR)"
  >
    <a menu-link href="#/reports/uses" icon="md md-assignment"
      >Consulta de Usos</a
    >
  </ul>

  <ul class="menu-links" ng-cloak ng-if="account.anyRole(roles.USER)">
    <a menu-link href="#/user-dashboard" icon="md md-blur-on">Dashboard</a>
    <a menu-link href="#/user-reports" icon="md md-assignment"
      >Reportes de Uso</a
    >
    <a menu-link href="#/user-account" icon="md md-account-circle">Mi Cuenta</a>
  </ul>

  <ul
    class="menu-links"
    ng-cloak
    ng-if="account.anyRole(roles.MASTER, roles.ASSISTANT)"
  >
    <a menu-link href="#/tiny-url" icon="fa fa-qrcode">URLs</a>
  </ul>

  <ul
    class="menu-links"
    ng-cloak
    ng-if="account.user.role === 'MASTER' || account.user.role === 'DEVELOPER'"
  >
    <a menu-link href="#/utilities" icon="md md-developer-mode"
      >Dev Utilities</a
    >
  </ul>

  <ul
    class="menu-links"
    ng-cloak
    ng-if="account.user.role === 'MASTER' || account.user.role === 'DEVELOPER'"
  >
    <li menu-toggle name="Totems" icon="md md-settings-cell">
      <a menu-link href="#/totems-machines" icon="md md-local-laundry-service"
        >Mapeo Máquinas</a
      >
      <a menu-link href="#/totems-pos" icon="md md-tap-and-play">Mapeo Pos</a>
    </li>
  </ul>
</aside>
